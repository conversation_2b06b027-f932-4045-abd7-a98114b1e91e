#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试答案显示功能
验证自动多行显示是否正常工作
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from experiment_display import ExperimentDisplay

def test_answer_display():
    """测试答案显示功能"""
    print("="*50)
    print("测试答案显示功能")
    print("="*50)
    
    # 创建显示对象（非全屏便于测试）
    display = ExperimentDisplay(fullscreen=False)
    
    try:
        # 测试短答案
        print("\n1. 测试短答案...")
        short_answer = "这是一个简短的答案。"
        display.show_answer(short_answer, 3.0)
        
        # 测试中等长度答案
        print("\n2. 测试中等长度答案...")
        medium_answer = "这是一个中等长度的答案，包含了更多的信息和详细的解释， 用来测试自动换行功能是否正常工作。"
        display.show_answer(medium_answer, 3.0)
        
        # 测试长答案
        print("\n3. 测试长答案...")
        long_answer = """这是一个非常长的答案， 包含了大量的文字内容。 它用来测试在1920*1080分辨率的AOC G2460PF显示器上， 文本是否能够正确地自动换行显示， 而不会超出屏幕边界。这个答案包含了多个句子， 每个句子都有不同的长度，用来全面测试文本显示的各种情况。 答案中还包含了一些专业术语和详细的解释，比如心理学实验中的好奇心测量、 瞳孔反应记录、 眼动追踪技术等等。通过这样的测试，我们可以确保所有类型的答案都能在屏幕上正确显示，给被试提供良好的实验体验。"""
        display.show_answer(long_answer, 5.0)
        
        # 测试问题显示
        print("\n4. 测试问题显示...")
        long_question = "这是一个比较长的问题，用来测试问题显示功能是否也能正确地自动换行。 问题的内容可能包含复杂的描述和多个条件， 需要确保在屏幕上完整可见？"
        display.show_question(long_question, 3.0)
        
        print("\n✓ 答案显示测试完成")
        return True
        
    except Exception as e:
        print(f"✗ 测试出错: {e}")
        return False
    finally:
        # 清理资源
        try:
            display.close()
            print("✓ 资源清理完成")
        except:
            pass

def main():
    """主函数"""
    print("测试答案自动多行显示功能")
    print("适配AOC G2460PF显示器 (1920*1080)")
    print("按Esc键可以跳过每个显示阶段")
    
    input("\n按回车键开始测试...")
    
    success = test_answer_display()
    
    if success:
        print("\n" + "="*50)
        print("✓ 答案显示测试通过！")
        print("文本能够正确自动换行显示")
        print("="*50)
    else:
        print("\n" + "="*50)
        print("✗ 答案显示测试失败")
        print("="*50)

if __name__ == "__main__":
    main()
