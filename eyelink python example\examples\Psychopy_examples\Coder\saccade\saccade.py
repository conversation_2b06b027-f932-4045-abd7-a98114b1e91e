#
# Copyright (c) 1996-2024, SR Research Ltd., All Rights Reserved
#
# For use by SR Research licencees only. Redistribution and use in source
# and binary forms, with or without modification, are NOT permitted.
#
# Redistributions in binary form must reproduce the above copyright
# notice, this list of conditions and the following disclaimer in
# the documentation and/or other materials provided with the distribution.
#
# Neither name of SR Research Ltd nor the name of contributors may be used
# to endorse or promote products derived from this software without
# specific prior written permission.
#
# THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS ``AS
# IS'' AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED
# TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A
# PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE REGENTS OR
# CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,
# EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
# PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR
# PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF
# LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING
# NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
# SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
#
# DESCRIPTION:
# This example scripts shows how retrieve to eye events (saccades) during
# testing. A visual target appears on the leftside or right side of the
# screen and the participant is required to quickly shift gaze to look
# at the target (pro-saccade) or a mirror location on the opposite side
# of the central fixation (anti-saccade).

# Last updated: 9/19/2024

from __future__ import division
from __future__ import print_function

import pylink
import os
import platform
import random
import time
import sys
from EyeLinkCoreGraphicsPsychoPy import EyeLinkCoreGraphicsPsychoPy
from psychopy import visual, core, event, monitors, gui
from psychopy.hardware import keyboard
from math import fabs, hypot
from string import ascii_letters, digits

# Switch to the script folder
script_path = os.path.dirname(sys.argv[0])
if len(script_path) != 0:
    os.chdir(script_path)

# Show only critical log message in the PsychoPy console
from psychopy import logging
logging.console.setLevel(logging.CRITICAL)

# Set this variable to True if you use the built-in retina screen or an 
# external High Resolution (2k, 4k, 5k) external monitor as your
# primary display device on macOS. 
# Make sure also to choose to optimize for the relevant monitor  
# in the Displays preference settings. 
# (e.g., "Optimize for Built-in Retina Display")
use_retina = False
# For macOS users check if they have a retina/H screen
if 'Darwin' in platform.system():
    dlg = gui.Dlg("Retina Screen?")
    dlg.addText("What type of screen will the experiment run on?")
    dlg.addField("Screen Type", choices=["High Resolution (Retina, 2k, 4k, 5k)", "Standard Resolution (HD or lower)"])
    # show dialog and wait for OK or Cancel
    ok_data = dlg.show()
    if dlg.OK:
        if dlg.data["Screen Type"] == "High Resolution (Retina, 2k, 4k, 5k)":  
            use_retina = True
        else:
            use_retina = False
    else:
        print('user cancelled')
        core.quit()
        sys.exit()

# Set this variable to True to run the script in "Dummy Mode"
dummy_mode = False

# Set this variable to True to run the task in full screen mode
# It is easier to debug the script in non-fullscreen mode
full_screen = True

# Store the parameters of all trials in a list, here we block the
# anti- and pro-saccade trials
# [cond, tar_pos, correct_sac_tar_pos]
pro_trials = [
    ['pro', 'left', 'left'],
    ['pro', 'right', 'right']
    ]

anti_trials = [
    ['anti', 'left', 'right'],
    ['anti', 'right', 'left']
    ]

# use a dictionary to label target position (left vs. right)
tar_pos = {'left': (-350, 0), 'right': (350, 0)}

# Set up EDF data file name and local data folder
#
# The EDF data filename should not exceed 8 alphanumeric characters
# use ONLY number 0-9, letters, & _ (underscore) in the filename
edf_fname = 'TEST'

# Prompt user to specify an EDF data filename
# before we open a fullscreen window
dlg_title = "Enter EDF Filename"
dlg_prompt = "Please enter a file name with 8 or fewer characters [letters, numbers, and underscore]."
# loop until we get a valid filename
while True:
    dlg = gui.Dlg(dlg_title)
    dlg.addText(dlg_prompt)
    dlg.addField("Filename", "EDF Filename:","Test")
    # show dialog and wait for OK or Cancel
    ok_data = dlg.show()
    if dlg.OK:  # if ok_data is not None
        print("EDF data filename: {}".format(ok_data["Filename"]))
    else:
        print("user cancelled")
        core.quit()
        sys.exit()

    # get the string entered by the experimenter
    tmp_str = ok_data["Filename"]
    # strip trailing characters, ignore the ".edf" extension
    edf_fname = tmp_str.rstrip().split(".")[0]

    # check if the filename is valid (length <= 8 & no special char)
    allowed_char = ascii_letters + digits + '_'
    if not all([c in allowed_char for c in edf_fname]):
        print('ERROR: Invalid EDF filename')
    elif len(edf_fname) > 8:
        print('ERROR: EDF filename should not exceed 8 characters')
    else:
        break

# Set up a folder to store the EDF data files and the associated resources
# e.g., files defining the interest areas used in each trial
results_folder = 'results'
if not os.path.exists(results_folder):
    os.makedirs(results_folder)

# We download EDF data file from the EyeLink Host PC to the local hard
# drive at the end of each testing session, here we rename the EDF to
# include session start date/time
time_str = time.strftime("_%Y_%m_%d_%H_%M", time.localtime())
session_identifier = edf_fname + time_str

# create a folder for the current testing session in the "results" folder
session_folder = os.path.join(results_folder, session_identifier)
if not os.path.exists(session_folder):
    os.makedirs(session_folder)

# Step 1: Connect to the EyeLink Host PC
#
# The Host IP address, by default, is "*********".
# the "el_tracker" objected created here can be accessed through the Pylink
# Set the Host PC address to "None" (without quotes) to run the script
# in "Dummy Mode"
if dummy_mode:
    el_tracker = pylink.EyeLink(None)
else:
    try:
        el_tracker = pylink.EyeLink("*********")
    except RuntimeError as error:
        print('ERROR:', error)
        core.quit()
        sys.exit()

# Step 2: Open an EDF data file on the Host PC
edf_file = edf_fname + ".EDF"
try:
    el_tracker.openDataFile(edf_file)
except RuntimeError as err:
    print('ERROR:', err)
    # close the link if we have one open
    if el_tracker.isConnected():
        el_tracker.close()
    core.quit()
    sys.exit()

# Add a header text to the EDF file to identify the current experiment name
# This is OPTIONAL. If your text starts with "RECORDED BY " it will be
# available in DataViewer's Inspector window by clicking
# the EDF session node in the top panel and looking for the "Recorded By:"
# field in the bottom panel of the Inspector.
preamble_text = 'RECORDED BY %s' % os.path.basename(__file__)
el_tracker.sendCommand("add_file_preamble_text '%s'" % preamble_text)

# Step 3: Configure the tracker
#
# Put the tracker in offline mode before we change tracking parameters
el_tracker.setOfflineMode()

# Get the software version:  1-EyeLink I, 2-EyeLink II, 3/4-EyeLink 1000,
# 5-EyeLink 1000 Plus, 6-Portable DUO
eyelink_ver = 0  # set version to 0, in case running in Dummy mode
if not dummy_mode:
    vstr = el_tracker.getTrackerVersionString()
    eyelink_ver = int(vstr.split()[-1].split('.')[0])
    # print out some version info in the shell
    print('Running experiment on %s, version %d' % (vstr, eyelink_ver))

# File and Link data control
# what eye events to save in the EDF file, include everything by default
file_event_flags = 'LEFT,RIGHT,FIXATION,SACCADE,BLINK,MESSAGE,BUTTON,INPUT'
# what eye events to make available over the link, include everything by default
link_event_flags = 'LEFT,RIGHT,FIXATION,SACCADE,BLINK,BUTTON,FIXUPDATE,INPUT'
# what sample data to save in the EDF data file and to make available
# over the link, include the 'HTARGET' flag to save head target sticker
# data for supported eye trackers
if eyelink_ver > 3:
    file_sample_flags = 'LEFT,RIGHT,GAZE,HREF,RAW,AREA,HTARGET,GAZERES,BUTTON,STATUS,INPUT'
    link_sample_flags = 'LEFT,RIGHT,GAZE,GAZERES,AREA,HTARGET,STATUS,INPUT'
else:
    file_sample_flags = 'LEFT,RIGHT,GAZE,HREF,RAW,AREA,GAZERES,BUTTON,STATUS,INPUT'
    link_sample_flags = 'LEFT,RIGHT,GAZE,GAZERES,AREA,STATUS,INPUT'
el_tracker.sendCommand("file_event_filter = %s" % file_event_flags)
el_tracker.sendCommand("file_sample_data = %s" % file_sample_flags)
el_tracker.sendCommand("link_event_filter = %s" % link_event_flags)
el_tracker.sendCommand("link_sample_data = %s" % link_sample_flags)

# Optional tracking parameters
# Sample rate, 250, 500, 1000, or 2000, check your tracker specification
# if eyelink_ver > 2:
#     el_tracker.sendCommand("sample_rate 1000")
# Choose a calibration type, H3, HV3, HV5, HV13 (HV = horizontal/vertical),
el_tracker.sendCommand("calibration_type = HV9")
# Set a gamepad button to accept calibration/drift check target
# You need a supported gamepad/button box that is connected to the Host PC
el_tracker.sendCommand("button_function 5 'accept_target_fixation'")

# Step 4: set up a graphics environment for calibration
#
# Open a window, be sure to specify monitor parameters
mon = monitors.Monitor('myMonitor', width=53.0, distance=70.0)
win = visual.Window(fullscr=full_screen,
                    monitor=mon,
                    winType='pyglet',
                    units='pix')

# get the native screen resolution used by PsychoPy
scn_width, scn_height = win.size
# resolution fix for Mac retina displays
if 'Darwin' in platform.system():
    if use_retina:
        scn_width = int(scn_width/2.0)
        scn_height = int(scn_height/2.0)

# Pass the display pixel coordinates (left, top, right, bottom) to the tracker
# see the EyeLink Installation Guide, "Customizing Screen Settings"
el_coords = "screen_pixel_coords = 0 0 %d %d" % (scn_width - 1, scn_height - 1)
el_tracker.sendCommand(el_coords)

# Write a DISPLAY_COORDS message to the EDF file
# Data Viewer needs this piece of info for proper visualization, see Data
# Viewer User Manual, "Protocol for EyeLink Data to Viewer Integration"
dv_coords = "DISPLAY_COORDS  0 0 %d %d" % (scn_width - 1, scn_height - 1)
el_tracker.sendMessage(dv_coords)

# Configure a graphics environment (genv) for tracker calibration
genv = EyeLinkCoreGraphicsPsychoPy(el_tracker, win)
print(genv)  # print out the version number of the CoreGraphics library

# Set background and foreground colors for the calibration target
# in PsychoPy, (-1, -1, -1)=black, (1, 1, 1)=white, (0, 0, 0)=mid-gray
foreground_color = (-1, -1, -1)
background_color = win.color
genv.setCalibrationColors(foreground_color, background_color)

# Set up the calibration target
#
# The target could be a "circle" (default), a "picture", a "movie" clip,
# or a rotating "spiral". To configure the type of calibration target, set
# genv.setTargetType to "circle", "picture", "movie", or "spiral", e.g.,
# genv.setTargetType('picture')
#
# Use gen.setPictureTarget() to set a "picture" target
# genv.setPictureTarget(os.path.join('images', 'fixTarget.bmp'))
#
# Use genv.setMovieTarget() to set a "movie" target
# genv.setMovieTarget(os.path.join('videos', 'calibVid.mov'))

# Use the default calibration target ('circle')
genv.setTargetType('circle')

# Configure the size of the calibration target (in pixels)
# this option applies only to "circle", "spiral", and "movie" targets
genv.setTargetSize(24)

# Beeps to play during calibration, validation and drift correction
# parameters: target, good, error
#     target -- sound to play when target moves
#     good -- sound to play on successful operation
#     error -- sound to play on failure or interruption
# Each parameter could be ''--default sound, 'off'--no sound, or a wav file
genv.setCalibrationSounds('', '', '')

# resolution fix for macOS retina display issues
if use_retina:
    genv.fixMacRetinaDisplay()

# Request Pylink to use the PsychoPy window we opened above for calibration
pylink.openGraphicsEx(genv)


# define a few helper functions for trial handling


def clear_screen(win):
    """ clear up the PsychoPy window"""

    win.fillColor = genv.getBackgroundColor()
    win.flip()


def show_msg(win, text, wait_for_keypress=True):
    """ Show task instructions on screen"""

    msg = visual.TextStim(win, text,
                          color=genv.getForegroundColor(),
                          wrapWidth=scn_width/2)
    clear_screen(win)
    msg.draw()
    win.flip()

    # wait indefinitely, terminates upon any key press
    if wait_for_keypress:
        event.waitKeys()
        clear_screen(win)


def terminate_task():
    """ Terminate the task gracefully and retrieve the EDF data file

    file_to_retrieve: The EDF on the Host that we would like to download
    win: the current window used by the experimental script
    """

    el_tracker = pylink.getEYELINK()

    if el_tracker.isConnected():
        # Terminate the current trial first if the task terminated prematurely
        error = el_tracker.isRecording()
        if error == pylink.TRIAL_OK:
            abort_trial()

        # Put tracker in Offline mode
        el_tracker.setOfflineMode()

        # Clear the Host PC screen and wait for 500 ms
        el_tracker.sendCommand('clear_screen 0')
        pylink.msecDelay(500)

        # Close the edf data file on the Host
        el_tracker.closeDataFile()

        # Show a file transfer message on the screen
        msg = 'EDF data is transferring from EyeLink Host PC...'
        show_msg(win, msg, wait_for_keypress=False)

        # Download the EDF data file from the Host PC to a local data folder
        # parameters: source_file_on_the_host, destination_file_on_local_drive
        local_edf = os.path.join(session_folder, session_identifier + '.EDF')
        try:
            el_tracker.receiveDataFile(edf_file, local_edf)
        except RuntimeError as error:
            print('ERROR:', error)

        # Close the link to the tracker.
        el_tracker.close()

    # close the PsychoPy window
    win.close()

    # quit PsychoPy
    core.quit()
    sys.exit()


def abort_trial():
    """Ends recording """

    el_tracker = pylink.getEYELINK()

    # Stop recording
    if el_tracker.isRecording():
        # add 100 ms to catch final trial events
        pylink.pumpDelay(100)
        el_tracker.stopRecording()

    # clear the screen
    clear_screen(win)
    # Send a message to clear the Data Viewer screen
    bgcolor_RGB = (116, 116, 116)
    el_tracker.sendMessage('!V CLEAR %d %d %d' % bgcolor_RGB)

    # send a message to mark trial end
    el_tracker.sendMessage('TRIAL_RESULT %d' % pylink.TRIAL_ERROR)

    return pylink.TRIAL_ERROR


def run_trial(trial_pars, trial_index):
    """ Helper function specifying the events that will occur in a single trial

    trial_pars - a list containing trial parameters, e.g.,
                 ['cond', 'left', 'left']
    trial_index - record the order of trial presentation in the task
    """

    # unpacking the trial parameters
    cond, vis_tar_pos, sac_tar_pos = trial_pars
    vis_x, vis_y = tar_pos[vis_tar_pos]
    sac_x, sac_y = tar_pos[sac_tar_pos]

    # prepare the target dot and the central fixation cross
    target = visual.GratingStim(win, tex=None, mask='circle', size=50,
                                pos=(vis_x, vis_y), color=(1, -1, -1))
    fix_cross = visual.TextStim(win, '+', height=50, color=(-1, 1, -1))

    # get a reference to the currently active EyeLink connection
    el_tracker = pylink.getEYELINK()

    # set up a keyboard object to check for key presses
    kb = keyboard.Keyboard()

    # put the tracker in the offline mode first
    el_tracker.setOfflineMode()

    # draw landmarks on the Host PC screen to mark the fixation cross,
    # the visual target position, and the landing position of correct saccade
    # The color codes supported on the Host PC range between 0-15
    # 0 - black, 1 - blue, 2 - green, 3 - cyan, 4 - red, 5 - magenta,
    # 6 - brown, 7 - light gray, 8 - dark gray, 9 - light blue,
    # 10 - light green, 11 - light cyan, 12 - light red,
    # 13 - bright magenta,  14 - yellow, 15 - bright white;
    # see /elcl/exe/COMMANDs.INI on the Host
    cross_coords = (int(scn_width/2.0), int(scn_height/2.0))
    sac_tar_coords = (int(scn_width/2 + sac_x - 100),
                      int(scn_height/2 - 60),
                      int(scn_width/2 + sac_x + 100),
                      int(scn_height/2 + 60))
    # the mirror location of the correct target
    mir_tar_coords = (int(scn_width/2 - sac_x - 100),
                      int(scn_height/2 - 60),
                      int(scn_width/2 - sac_x + 100),
                      int(scn_height/2 + 60))
    el_tracker.sendCommand('clear_screen 0')  # clear the host Display
    el_tracker.sendCommand('draw_cross %d %d 10' % cross_coords)  # draw cross
    el_tracker.sendCommand('draw_box %d %d %d %d 10' % sac_tar_coords)
    el_tracker.sendCommand('draw_box %d %d %d %d 12' % mir_tar_coords)

    # send a "TRIALID" message to mark the start of a trial, see Data
    # Viewer User Manual, "Protocol for EyeLink Data to Viewer Integration"
    el_tracker.sendMessage('TRIALID %d' % trial_index)

    # record_status_message : show some info on the Host PC
    # here we show how many trial has been tested
    status_msg = 'TRIAL number %d, %s' % (trial_index, cond)
    el_tracker.sendCommand("record_status_message '%s'" % status_msg)

    # drift check
    # we recommend drift-check at the beginning of each trial
    # the doDriftCorrect() function requires target position in integers
    # the last two arguments:
    # draw_target (1-default, 0-draw the target then call doDriftCorrect)
    # allow_setup (1-press ESCAPE to recalibrate, 0-not allowed)
    #
    # Skip drift-check if running the script in Dummy Mode
    while not dummy_mode:
        # terminate the task if no longer connected to the tracker or
        # user pressed Ctrl-C to terminate the task
        if (not el_tracker.isConnected()) or el_tracker.breakPressed():
            terminate_task()
            return pylink.ABORT_EXPT

        # drift-check and re-do camera setup if ESCAPE is pressed
        try:
            error = el_tracker.doDriftCorrect(int(scn_width/2.0),
                                              int(scn_height/2.0), 1, 1)
            # break following a success drift-check
            if error is not pylink.ESC_KEY:
                break
        except:
            pass

    # put tracker in idle/offline mode before recording
    el_tracker.setOfflineMode()

    # Start recording
    # arguments: sample_to_file, events_to_file, sample_over_link,
    # event_over_link (1-yes, 0-no)
    try:
        el_tracker.startRecording(1, 1, 1, 1)
    except RuntimeError as error:
        print("ERROR:", error)
        abort_trial()
        return pylink.TRIAL_ERROR

    # Allocate some time for the tracker to cache some samples
    pylink.pumpDelay(100)

    # determine which eye(s) is/are available
    # 0- left, 1-right, 2-binocular
    eye_used = el_tracker.eyeAvailable()
    if eye_used == 1:
        el_tracker.sendMessage("EYE_USED 1 RIGHT")
    elif eye_used == 0 or eye_used == 2:
        el_tracker.sendMessage("EYE_USED 0 LEFT")
        eye_used = 0
    else:
        print("Error in getting the eye information!")
        return pylink.TRIAL_ERROR

    # put the central fixation cross on screen for 1000 ms
    fix_cross.draw()
    win.flip()
    # send over a message to log the onset of the fixation cross
    el_tracker.sendMessage('fix_onset')

    # OPTIONAL - send over another message to request Data Viewer to draw
    # a cross when visualizing the data; see Data Viewer User Manual,
    # Protocol for EyeLink Data to Viewer Integration
    h_center = int(scn_width/2.0)
    v_center = int(scn_height/2.0)
    line_hor = (h_center - 20, v_center, h_center + 20, v_center)
    line_ver = (h_center, v_center - 20, h_center, v_center + 20)
    el_tracker.sendMessage('!V CLEAR 128 128 128')  # clear the screen
    el_tracker.sendMessage('!V DRAWLINE 0 255 0 %d %d %d %d' % line_hor)
    el_tracker.sendMessage('!V DRAWLINE 0 255 0 %d %d %d %d' % line_ver)

    # fixation duration = 1000 ms
    core.wait(1.0)

    # show the target on screen
    target.draw()
    win.flip()

    # send over a message to log the onset of the target
    el_tracker.sendMessage('target_onset')

    # the saccade events retrieved are timestamped according to the timer of
    # the Host PC; to properly calculate an saccadic response time,
    # read the tracker time here
    tar_onset_time = el_tracker.trackerTime()

    # set an interest area to mark the hit region for the target; see Data
    # Viewer User Manual, Protocol for EyeLink Data to Viewer Integration
    hit_region = (int(scn_width/2.0 + sac_x-100),
                  int(scn_height/2.0 - 100),
                  int(scn_width/2.0 + sac_x + 100),
                  int(scn_height/2.0 + 100))
    target_ia_msg = '!V IAREA RECTANGLE 1 %d %d %d %d correct_IA' % hit_region
    el_tracker.sendMessage(target_ia_msg)
    # draw another interest area to mark the location mirroring the hit_region
    mir_region = (int(scn_width/2.0 - sac_x-100),
                  int(scn_height/2.0 - 100),
                  int(scn_width/2.0 - sac_x + 100),
                  int(scn_height/2.0 + 100))
    mirror_ia_msg = '!V IAREA RECTANGLE 2 %d %d %d %d incorrect_IA' % mir_region
    el_tracker.sendMessage(mirror_ia_msg)

    # OPTIONAL - send over another message to request Data Viewer to draw the
    # visual target when visualizing the data
    el_tracker.sendMessage('!V CLEAR 128 128 128')  # clear the screen
    vis_tar = (int(scn_width/2.0 + vis_x),  int(scn_height/2.0))
    el_tracker.sendMessage('!V FIXPOINT 255 0 0 255 0 0 %d %d 50 50' % vis_tar)

    # wait until a reasonably large saccade occurs or a key is pressed
    got_sac = False
    sac_start_time = -1
    SRT = -1  # initialize a variable to store saccadic reaction time (SRT)
    land_err = -1  # landing error of the saccade
    acc = 0  # hit the correct region or not

    event.clearEvents()  # clear all cached events if there are any
    while not got_sac:
        # present the target for a maximum of 3 seconds
        if el_tracker.trackerTime() - tar_onset_time >= 3000:
            el_tracker.sendMessage('time_out')
            break

        # abort the current trial if the tracker is no longer recording
        error = el_tracker.isRecording()
        if error is not pylink.TRIAL_OK:
            el_tracker.sendMessage('tracker_disconnected')
            abort_trial()
            return error

        # check keyboard events
        keyPressList = kb.getKeys(keyList = None, waitRelease = False, clear = False)
        if len(keyPressList) > 0:
            keyPressNamesList = list()
            for keyPress in keyPressList:
                keyPressNamesList.append(keyPress.name)

            # Abort a trial if "ESCAPE" is pressed
            if 'escape' in keyPressNamesList:
                el_tracker.sendMessage('trial_skipped_by_user')
                # clear the screen
                clear_screen(win)
                # abort trial
                abort_trial()
                return pylink.SKIP_TRIAL

            # Terminate the task if Ctrl-c
            if 'c' in keyPressNamesList and ('lctrl' in keyPressNamesList or 'rctrl' in keyPressNamesList):
                el_tracker.sendMessage('terminated_by_user')
                terminate_task()
                return pylink.ABORT_EXPT

        # grab the events in the buffer, for more details,
        # see the example script "link_event.py"
        eye_ev = el_tracker.getNextData()
        if (eye_ev is not None) and (eye_ev == pylink.ENDSACC):
            eye_dat = el_tracker.getFloatData()
            if eye_dat.getEye() == eye_used:
                sac_amp = eye_dat.getAmplitude()  # amplitude
                sac_start_time = eye_dat.getStartTime()  # onset time
                sac_end_time = eye_dat.getEndTime()  # offset time
                sac_start_pos = eye_dat.getStartGaze()  # start position
                sac_end_pos = eye_dat.getEndGaze()  # end position

                # a saccade was initiated
                if sac_start_time <= tar_onset_time:
                    sac_start_time = -1
                    pass  # ignore saccades occurred before target onset
                elif hypot(sac_amp[0], sac_amp[1]) > 1.5:
                    # log a message to mark the time at which a saccadic
                    # response occurred; note that, here we are detecting a
                    # saccade END event; the saccade actually occurred some
                    # msecs ago. The following message has an additional
                    # time offset, so Data Viewer knows when exactly the
                    # "saccade_resp" event actually happened
                    offset = int(el_tracker.trackerTime()-sac_start_time)
                    sac_response_msg = '{} saccade_resp'.format(offset)
                    el_tracker.sendMessage(sac_response_msg)
                    SRT = sac_start_time - tar_onset_time

                    # count as a correct reponse if the saccade lands in
                    # the square shaped hit region
                    sac_x = sac_x + scn_width/2.0
                    sac_y = sac_y + scn_height/2.0
                    if fabs(sac_end_pos[0] - sac_x) < 100 and \
                       fabs(sac_end_pos[1] - sac_y) < 100:
                        acc = 1
                    else:
                        acc = 0

                    got_sac = True

    # following the saccadic response, show the target for an additional 300 ms
    core.wait(0.3)

    # clear the screen
    clear_screen(win)
    el_tracker.sendMessage('blank_screen')
    # send a message to clear the Data Viewer screen as well
    el_tracker.sendMessage('!V CLEAR 128 128 128')

    # stop recording; add 100 msec to catch final events before stopping
    pylink.pumpDelay(100)
    el_tracker.stopRecording()

    # record trial variables to the EDF data file, for details, see Data
    # Viewer User Manual, "Protocol for EyeLink Data to Viewer Integration"
    el_tracker.sendMessage('!V TRIAL_VAR condition %s' % cond)
    el_tracker.sendMessage('!V TRIAL_VAR visual_target_pos %s' % vis_tar_pos)
    el_tracker.sendMessage('!V TRIAL_VAR saccade_target_pos %s' % sac_tar_pos)
    pylink.msecDelay(4)  # take a break of 4 millisecond
    el_tracker.sendMessage('!V TRIAL_VAR tar_onset_time %d' % tar_onset_time)
    el_tracker.sendMessage('!V TRIAL_VAR sac_start_time %d' % sac_start_time)
    el_tracker.sendMessage('!V TRIAL_VAR SRT %d' % SRT)
    el_tracker.sendMessage('!V TRIAL_VAR acc %d' % acc)

    # send a 'TRIAL_RESULT' message to mark the end of trial, see Data
    # Viewer User Manual, "Protocol for EyeLink Data to Viewer Integration"
    el_tracker.sendMessage('TRIAL_RESULT %d' % pylink.TRIAL_OK)


# Step 5: Run 2 blocks of trials
trial_index = 1
for block in ['pro', 'anti']:
    # Show task instructions
    if block == 'anti':  # anti-saccade block
        task_msg = 'Anti-saccade block\n\n' + \
            'Shift gaze to the mirror location of the target ASAP\n'
    else:
        task_msg = 'Pro-saccade block\n\n' + \
            'Shift gaze to the target location ASAP\n'
    if dummy_mode:
        task_msg = 'Cannot run the script in Dummy mode,\n' + \
            'press ENTER to terminate the task.'
    else:
        task_msg = task_msg + \
            'Press Ctrl-C to if you need to quit the task early\n' + \
            '\nNow, press ENTER twice to calibrate tracker'
    show_msg(win, task_msg)

    # Terminate the task if running in Dummy Mode
    if dummy_mode:
        print('ERROR: This task requires real-time gaze data.\n' +
              'It cannot run in Dummy mode (with no tracker connection).')
        terminate_task()
    else:  # calibrate the tracker
        try:
            el_tracker.doTrackerSetup()
        except RuntimeError as err:
            print('ERROR:', err)
            el_tracker.exitCalibration()

    # construct a trial list
    if block == 'anti':
        test_list = anti_trials[:] * 2
    if block == 'pro':
        test_list = pro_trials[:] * 2

    # randomize the trial list
    random.shuffle(test_list)

    if block == 'pro':
        block_num = 0
    else:
        block_num = 1

    trial_index = block_num*len(test_list) + 1
    for trial_pars in test_list:
        run_trial(trial_pars, trial_index)
        trial_index += 1

# Step 6: disconnect, download the EDF file, then terminate the task
terminate_task()
