#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
检查实验所需的依赖库
"""

import sys
import importlib

def check_package(package_name, import_name=None):
    """检查包是否已安装"""
    if import_name is None:
        import_name = package_name
    
    try:
        module = importlib.import_module(import_name)
        version = getattr(module, '__version__', 'Unknown version')
        print(f"✓ {package_name}: {version}")
        return True
    except ImportError:
        print(f"✗ {package_name}: Not installed")
        return False

def main():
    print("检查实验依赖库...")
    print("=" * 50)
    
    required_packages = [
        ('PsychoPy', 'psychopy'),
        ('PyLink (EyeLink)', 'pylink'),
        ('NumPy', 'numpy'),
        ('Pandas', 'pandas'),
        ('Random', 'random'),
        ('Time', 'time'),
        ('OS', 'os'),
        ('JSON', 'json'),
        ('DateTime', 'datetime')
    ]
    
    all_installed = True
    
    for package_name, import_name in required_packages:
        if not check_package(package_name, import_name):
            all_installed = False
    
    print("=" * 50)
    if all_installed:
        print("✓ 所有必需的依赖库都已安装！")
    else:
        print("✗ 部分依赖库缺失，需要安装")
        print("\n建议安装命令:")
        print("conda install psychopy")
        print("pip install pylink-platform")  # EyeLink的Python接口
    
    print(f"\nPython版本: {sys.version}")
    print(f"当前工作目录: {sys.path[0]}")

if __name__ == "__main__":
    main()
