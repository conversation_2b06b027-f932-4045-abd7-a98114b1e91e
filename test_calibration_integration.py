#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试校准集成功能
验证main_experiment中的校准功能是否正常工作
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from experiment_flow import CuriosityExperiment

def test_calibration_integration():
    """测试校准集成"""
    print("="*50)
    print("测试校准集成功能")
    print("="*50)
    
    # 创建实验对象（使用虚拟模式）
    experiment = CuriosityExperiment(
        participant_id="test_cal",
        use_eyelink=True,  # 启用EyeLink
        fullscreen=False   # 非全屏便于测试
    )
    
    try:
        print("\n1. 初始化实验组件...")
        if not experiment.initialize_components():
            print("✗ 初始化失败")
            return False
        
        print("✓ 初始化成功")
        
        print("\n2. 测试校准功能...")
        
        # 检查EyeLink管理器是否有display_window
        if experiment.eyelink:
            print(f"EyeLink管理器状态:")
            print(f"  - 连接状态: {experiment.eyelink.is_connected}")
            print(f"  - 虚拟模式: {experiment.eyelink.dummy_mode}")
            print(f"  - 显示窗口: {'已设置' if experiment.eyelink.display_window else '未设置'}")
            
            # 测试校准方法
            print("\n开始校准测试...")
            success = experiment.run_calibration()
            
            if success:
                print("✓ 校准测试成功")
            else:
                print("✗ 校准测试失败")
                
            return success
        else:
            print("✗ EyeLink管理器未初始化")
            return False
            
    except Exception as e:
        print(f"✗ 测试出错: {e}")
        return False
    finally:
        # 清理资源
        try:
            experiment.cleanup()
            print("✓ 资源清理完成")
        except:
            pass

def main():
    """主函数"""
    success = test_calibration_integration()
    
    if success:
        print("\n" + "="*50)
        print("✓ 校准集成测试通过！")
        print("main_experiment.py中的校准功能已成功集成")
        print("="*50)
    else:
        print("\n" + "="*50)
        print("✗ 校准集成测试失败")
        print("="*50)

if __name__ == "__main__":
    main()
