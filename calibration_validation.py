#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
EyeLink校准和验证程序
使用PsychoPy调用校准函数，然后显示随机光点和实时眼动目标
"""

import pylink
import random
import time
import os
from psychopy import visual, core, event, monitors

# 尝试导入EyeLinkCoreGraphicsPsychoPy，如果失败则使用简化版本
try:
    from EyeLinkCoreGraphicsPsychoPy import EyeLinkCoreGraphicsPsychoPy
    GRAPHICS_AVAILABLE = True
except ImportError:
    print("警告：无法导入EyeLinkCoreGraphicsPsychoPy，将使用简化模式")
    GRAPHICS_AVAILABLE = False

class EyeLinkCalibrationValidation:
    """EyeLink校准和验证类"""
    
    def __init__(self, eyelink_ip="*********", dummy_mode=False):
        """
        初始化校准验证程序
        
        Args:
            eyelink_ip: EyeLink主机IP地址
            dummy_mode: 是否使用虚拟模式（用于测试）
        """
        self.eyelink_ip = eyelink_ip
        self.dummy_mode = dummy_mode
        self.el_tracker = None
        self.win = None
        self.genv = None
        
        # 验证参数
        self.dot_positions = []  # 光点位置列表
        self.current_dot_index = 0
        self.dot_switch_interval = 3.0  # 光点切换间隔（秒）
        self.last_switch_time = 0
        
        # 显示参数
        self.dot_size = 20  # 光点大小
        self.gaze_size = 15  # 眼动目标大小
        self.dot_color = 'white'
        self.gaze_color = 'red'
        self.background_color = 'gray'
        
    def connect_eyelink(self):
        """连接EyeLink眼动仪"""
        try:
            if self.dummy_mode:
                print("使用虚拟模式连接EyeLink...")
                self.el_tracker = pylink.EyeLink(None)
            else:
                print(f"连接EyeLink主机: {self.eyelink_ip}")
                self.el_tracker = pylink.EyeLink(self.eyelink_ip)
            
            print("✓ EyeLink连接成功")
            return True
            
        except RuntimeError as error:
            print(f"✗ EyeLink连接失败: {error}")
            return False
    
    def setup_display(self):
        """设置显示窗口"""
        try:
            # 创建显示器配置
            mon = monitors.Monitor('myMonitor', width=53.0, distance=70.0)
            
            # 创建全屏窗口
            self.win = visual.Window(
                fullscr=True,
                monitor=mon,
                winType='pyglet',
                units='pix',
                color=self.background_color
            )
            
            # 获取屏幕分辨率
            scn_width, scn_height = self.win.size
            print(f"屏幕分辨率: {scn_width} x {scn_height}")
            
            # 发送屏幕坐标给EyeLink
            if self.el_tracker:
                el_coords = f"screen_pixel_coords = 0 0 {scn_width - 1} {scn_height - 1}"
                self.el_tracker.sendCommand(el_coords)
                
                # 发送DISPLAY_COORDS消息给Data Viewer
                dv_coords = f"DISPLAY_COORDS 0 0 {scn_width - 1} {scn_height - 1}"
                self.el_tracker.sendMessage(dv_coords)
            
            print("✓ 显示窗口设置成功")
            return True
            
        except Exception as e:
            print(f"✗ 显示窗口设置失败: {e}")
            return False
    
    def setup_graphics_environment(self):
        """配置校准图形环境"""
        try:
            if GRAPHICS_AVAILABLE:
                # 创建图形环境
                self.genv = EyeLinkCoreGraphicsPsychoPy(self.el_tracker, self.win)

                # 设置校准颜色
                foreground_color = (-1, -1, -1)  # 黑色
                background_color = self.win.color
                self.genv.setCalibrationColors(foreground_color, background_color)

                # 设置校准目标类型为圆形
                self.genv.setTargetType('circle')

                # 设置校准提示音（使用默认声音）
                self.genv.setCalibrationSounds('', '', '')

                # 请求PyLink使用配置好的图形环境
                pylink.openGraphicsEx(self.genv)

                print("✓ 图形环境配置成功")
            else:
                print("✓ 使用简化模式（无图形环境）")

            return True

        except Exception as e:
            print(f"✗ 图形环境配置失败: {e}")
            return False
    
    def run_calibration(self):
        """执行校准流程"""
        try:
            print("\n开始校准流程...")

            if GRAPHICS_AVAILABLE:
                print("操作说明:")
                print("- 按 C 开始校准")
                print("- 按 V 开始验证")
                print("- 按 Enter 查看相机图像")
                print("- 按 Esc 退出校准")
                print("- 校准时请注视屏幕上出现的目标点")

                # 显示提示信息
                instruction_text = visual.TextStim(
                    self.win,
                    text="准备开始校准\n\n按任意键继续...",
                    font='SimHei',
                    height=40,
                    color='white',
                    pos=(0, 0)
                )
                instruction_text.draw()
                self.win.flip()

                # 等待按键
                event.waitKeys()

                # 进入校准设置
                self.el_tracker.doTrackerSetup()
            else:
                print("简化模式：跳过校准步骤")
                print("在实际使用中，请确保EyeLink已正确校准")
                time.sleep(2)

            print("✓ 校准完成")
            return True

        except RuntimeError as err:
            print(f"✗ 校准失败: {err}")
            if hasattr(self.el_tracker, 'exitCalibration'):
                self.el_tracker.exitCalibration()
            return False
    
    def generate_dot_positions(self, num_positions=9):
        """生成随机光点位置"""
        scn_width, scn_height = self.win.size
        margin = 100  # 边距
        
        self.dot_positions = []
        for _ in range(num_positions):
            x = random.randint(-scn_width//2 + margin, scn_width//2 - margin)
            y = random.randint(-scn_height//2 + margin, scn_height//2 - margin)
            self.dot_positions.append((x, y))
        
        print(f"生成了 {len(self.dot_positions)} 个随机光点位置")
    
    def get_current_gaze_position(self):
        """获取当前眼动位置"""
        if not self.el_tracker:
            return None
            
        try:
            # 获取最新的眼动样本
            dt = self.el_tracker.getNewestSample()
            if dt is not None:
                # 检查左眼或右眼数据
                if dt.isLeftSample():
                    gaze_pos = dt.getLeftEye().getGaze()
                elif dt.isRightSample():
                    gaze_pos = dt.getRightEye().getGaze()
                else:
                    return None
                
                # 转换坐标系（EyeLink坐标系转换为PsychoPy坐标系）
                scn_width, scn_height = self.win.size
                x = gaze_pos[0] - scn_width // 2
                y = scn_height // 2 - gaze_pos[1]
                
                return (x, y)
        except:
            pass
        
        return None
    
    def run_validation(self, duration=60):
        """运行验证程序"""
        try:
            print(f"\n开始验证程序，持续 {duration} 秒...")
            print("- 白色光点每3秒切换位置")
            print("- 红色圆圈显示您当前的注视位置")
            print("- 按 Esc 键退出")
            
            # 生成光点位置
            self.generate_dot_positions()
            
            # 开始记录眼动数据
            if self.el_tracker:
                self.el_tracker.startRecording(1, 1, 1, 1)
                pylink.pumpDelay(100)  # 等待记录开始
            
            # 创建显示对象
            dot_stim = visual.Circle(
                self.win,
                radius=self.dot_size,
                fillColor=self.dot_color,
                lineColor=self.dot_color
            )
            
            gaze_stim = visual.Circle(
                self.win,
                radius=self.gaze_size,
                fillColor=None,
                lineColor=self.gaze_color,
                lineWidth=3
            )
            
            # 验证循环
            start_time = time.time()
            self.last_switch_time = start_time
            self.current_dot_index = 0
            
            while time.time() - start_time < duration:
                current_time = time.time()
                
                # 检查是否需要切换光点位置
                if current_time - self.last_switch_time >= self.dot_switch_interval:
                    self.current_dot_index = (self.current_dot_index + 1) % len(self.dot_positions)
                    self.last_switch_time = current_time
                    print(f"切换到位置 {self.current_dot_index + 1}: {self.dot_positions[self.current_dot_index]}")
                
                # 设置当前光点位置
                dot_pos = self.dot_positions[self.current_dot_index]
                dot_stim.pos = dot_pos
                
                # 获取当前眼动位置
                gaze_pos = self.get_current_gaze_position()
                
                # 清屏并绘制
                self.win.color = self.background_color
                
                # 绘制光点
                dot_stim.draw()
                
                # 绘制眼动位置（如果有效）
                if gaze_pos:
                    gaze_stim.pos = gaze_pos
                    gaze_stim.draw()
                
                # 显示帧
                self.win.flip()
                
                # 检查退出键
                keys = event.getKeys()
                if 'escape' in keys:
                    print("用户按下Esc键，退出验证")
                    break
            
            # 停止记录
            if self.el_tracker:
                self.el_tracker.stopRecording()
            
            print("✓ 验证程序完成")
            return True
            
        except Exception as e:
            print(f"✗ 验证程序出错: {e}")
            return False
    
    def cleanup(self):
        """清理资源"""
        try:
            if self.el_tracker:
                # 关闭EyeLink连接
                self.el_tracker.close()

            if self.win:
                # 关闭显示窗口
                self.win.close()

            # 关闭图形环境（仅在可用时）
            if GRAPHICS_AVAILABLE:
                pylink.closeGraphics()

            print("✓ 资源清理完成")

        except Exception as e:
            print(f"清理资源时出错: {e}")

def main():
    """主函数"""
    print("="*50)
    print("EyeLink校准和验证程序")
    print("="*50)
    
    # 获取用户输入
    use_dummy = input("是否使用虚拟模式? (y/n, 默认n): ").strip().lower() == 'y'
    
    if not use_dummy:
        eyelink_ip = input("EyeLink IP地址 (默认*********): ").strip()
        if not eyelink_ip:
            eyelink_ip = "*********"
    else:
        eyelink_ip = "*********"
    
    validation_duration = input("验证持续时间(秒, 默认60): ").strip()
    try:
        validation_duration = int(validation_duration) if validation_duration else 60
    except ValueError:
        validation_duration = 60
    
    # 创建校准验证对象
    calibrator = EyeLinkCalibrationValidation(eyelink_ip, use_dummy)
    
    try:
        # 1. 连接EyeLink
        if not calibrator.connect_eyelink():
            return
        
        # 2. 设置显示
        if not calibrator.setup_display():
            return
        
        # 3. 配置图形环境
        if not calibrator.setup_graphics_environment():
            return
        
        # 4. 执行校准
        if not calibrator.run_calibration():
            return
        
        # 5. 运行验证
        calibrator.run_validation(validation_duration)
        
        print("\n程序执行完成！")
        
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"\n程序出现错误: {e}")
    finally:
        # 清理资源
        calibrator.cleanup()

if __name__ == "__main__":
    main()
