﻿<?xml version="1.0" ?>
<PsychoPy2experiment encoding="utf-8" version="2024.2.1post4">
  <Settings>
    <Param val="use prefs" valType="str" updates="None" name="Audio latency priority"/>
    <Param val="ptb" valType="str" updates="None" name="Audio lib"/>
    <Param val="" valType="str" updates="None" name="Completed URL"/>
    <Param val="auto" valType="str" updates="None" name="Data file delimiter"/>
    <Param val="u'data' + os.sep + '%s_%s' % (expInfo['participant'], expInfo['date'])" valType="code" updates="None" name="Data filename"/>
    <Param val="False" valType="bool" updates="None" name="Enable Escape"/>
    <Param val="" valType="str" updates="None" name="End Message"/>
    <Param val="{'session': '01', 'participant': ''}" valType="code" updates="None" name="Experiment info"/>
    <Param val="True" valType="bool" updates="None" name="Force stereo"/>
    <Param val="True" valType="bool" updates="None" name="Full-screen window"/>
    <Param val="html" valType="str" updates="None" name="HTML path"/>
    <Param val="" valType="str" updates="None" name="Incomplete URL"/>
    <Param val="packaged" valType="str" updates="None" name="JS libs"/>
    <Param val="testMonitor" valType="str" updates="None" name="Monitor"/>
    <Param val="[]" valType="list" updates="None" name="Resources"/>
    <Param val="False" valType="bool" updates="None" name="Save csv file"/>
    <Param val="True" valType="bool" updates="None" name="Save excel file"/>
    <Param val="False" valType="bool" updates="None" name="Save hdf5 file"/>
    <Param val="True" valType="bool" updates="None" name="Save log file"/>
    <Param val="True" valType="bool" updates="None" name="Save psydat file"/>
    <Param val="True" valType="bool" updates="None" name="Save wide csv file"/>
    <Param val="1" valType="num" updates="None" name="Screen"/>
    <Param val="True" valType="bool" updates="None" name="Show info dlg"/>
    <Param val="False" valType="bool" updates="None" name="Show mouse"/>
    <Param val="pix" valType="str" updates="None" name="Units"/>
    <Param val="" valType="str" updates="None" name="Use version"/>
    <Param val="[1920, 1080]" valType="code" updates="None" name="Window size (pixels)"/>
    <Param val="none" valType="str" updates="None" name="backgroundFit"/>
    <Param val="" valType="str" updates="None" name="backgroundImg"/>
    <Param val="avg" valType="str" updates="None" name="blendMode"/>
    <Param val="float" valType="str" updates="None" name="clockFormat"/>
    <Param val="{'thisRow.t': 'priority.CRITICAL', 'expName': 'priority.LOW'}" valType="dict" updates="None" name="colPriority"/>
    <Param val="$[0.0,0.0,0.0]" valType="str" updates="None" name="color"/>
    <Param val="rgb" valType="str" updates="None" name="colorSpace"/>
    <Param val="warning" valType="code" updates="None" name="consoleLoggingLevel"/>
    <Param val="default" valType="str" updates="None" name="ecSampleRate"/>
    <Param val="*********" valType="str" updates="None" name="elAddress"/>
    <Param val="FILTER_LEVEL_2" valType="str" updates="None" name="elDataFiltering"/>
    <Param val="FILTER_LEVEL_OFF" valType="str" updates="None" name="elLiveFiltering"/>
    <Param val="EYELINK 1000 DESKTOP" valType="str" updates="None" name="elModel"/>
    <Param val="ELLIPSE_FIT" valType="str" updates="None" name="elPupilAlgorithm"/>
    <Param val="PUPIL_AREA" valType="str" updates="None" name="elPupilMeasure"/>
    <Param val="1000" valType="num" updates="None" name="elSampleRate"/>
    <Param val="False" valType="bool" updates="None" name="elSimMode"/>
    <Param val="RIGHT_EYE" valType="str" updates="None" name="elTrackEyes"/>
    <Param val="PUPIL_CR_TRACKING" valType="str" updates="None" name="elTrackingMode"/>
    <Param val="EyeLinkVideo_Builder" valType="str" updates="None" name="expName"/>
    <Param val="on Sync" valType="str" updates="None" name="exportHTML"/>
    <Param val="None" valType="str" updates="None" name="eyetracker"/>
    <Param val="" valType="code" updates="None" name="frameRate"/>
    <Param val="Attempting to measure frame rate of screen, please wait..." valType="str" updates="None" name="frameRateMsg"/>
    <Param val="127.0.0.1" valType="str" updates="None" name="gpAddress"/>
    <Param val="4242" valType="num" updates="None" name="gpPort"/>
    <Param val="PsychToolbox" valType="str" updates="None" name="keyboardBackend"/>
    <Param val="error" valType="code" updates="None" name="logging level"/>
    <Param val="True" valType="bool" updates="None" name="measureFrameRate"/>
    <Param val="('MIDDLE_BUTTON',)" valType="list" updates="None" name="mgBlink"/>
    <Param val="CONTINUOUS" valType="str" updates="None" name="mgMove"/>
    <Param val="0.5" valType="num" updates="None" name="mgSaccade"/>
    <Param val="neon.local" valType="str" updates="None" name="plCompanionAddress"/>
    <Param val="8080" valType="num" updates="None" name="plCompanionPort"/>
    <Param val="True" valType="bool" updates="None" name="plCompanionRecordingEnabled"/>
    <Param val="0.6" valType="num" updates="None" name="plConfidenceThreshold"/>
    <Param val="True" valType="bool" updates="None" name="plPupilCaptureRecordingEnabled"/>
    <Param val="" valType="str" updates="None" name="plPupilCaptureRecordingLocation"/>
    <Param val="127.0.0.1" valType="str" updates="None" name="plPupilRemoteAddress"/>
    <Param val="50020" valType="num" updates="None" name="plPupilRemotePort"/>
    <Param val="1000" valType="num" updates="None" name="plPupilRemoteTimeoutMs"/>
    <Param val="False" valType="bool" updates="None" name="plPupillometryOnly"/>
    <Param val="psychopy_iohub_surface" valType="str" updates="None" name="plSurfaceName"/>
    <Param val="1" valType="code" updates="None" name="runMode"/>
    <Param val="False" valType="bool" updates="None" name="rush"/>
    <Param val="time" valType="str" updates="None" name="sortColumns"/>
    <Param val="" valType="str" updates="None" name="tbLicenseFile"/>
    <Param val="" valType="str" updates="None" name="tbModel"/>
    <Param val="60" valType="num" updates="None" name="tbSampleRate"/>
    <Param val="" valType="str" updates="None" name="tbSerialNo"/>
    <Param val="pyglet" valType="str" updates="None" name="winBackend"/>
  </Settings>
  <Routines>
    <Routine name="trial">
      <RoutineSettingsComponent name="trial" plugin="None">
        <Param val="none" valType="str" updates="None" name="backgroundFit"/>
        <Param val="" valType="str" updates="None" name="backgroundImg"/>
        <Param val="$[0,0,0]" valType="color" updates="None" name="color"/>
        <Param val="rgb" valType="str" updates="None" name="colorSpace"/>
        <Param val="" valType="str" updates="constant" name="desc"/>
        <Param val="False" valType="bool" updates="None" name="disabled"/>
        <Param val="" valType="code" updates="None" name="durationEstim"/>
        <Param val="False" valType="code" updates="None" name="forceNonSlip"/>
        <Param val="trial" valType="code" updates="None" name="name"/>
        <Param val="True" valType="bool" updates="None" name="saveStartStop"/>
        <Param val="" valType="code" updates="constant" name="skipIf"/>
        <Param val="duration (s)" valType="str" updates="None" name="stopType"/>
        <Param val="" valType="code" updates="constant" name="stopVal"/>
        <Param val="False" valType="bool" updates="None" name="useWindowParams"/>
      </RoutineSettingsComponent>
      <MovieComponent name="movie" plugin="None">
        <Param val="False" valType="bool" updates="None" name="No audio"/>
        <Param val="center" valType="str" updates="constant" name="anchor"/>
        <Param val="ffpyplayer" valType="str" updates="None" name="backend"/>
        <Param val="1" valType="num" updates="constant" name="contrast"/>
        <Param val="False" valType="bool" updates="None" name="disabled"/>
        <Param val="" valType="code" updates="None" name="durationEstim"/>
        <Param val="False" valType="bool" updates="constant" name="forceEndRoutine"/>
        <Param val="False" valType="bool" updates="None" name="loop"/>
        <Param val="$vidpath" valType="file" updates="set every repeat" name="movie"/>
        <Param val="movie" valType="code" updates="None" name="name"/>
        <Param val="" valType="num" updates="constant" name="opacity"/>
        <Param val="0" valType="num" updates="constant" name="ori"/>
        <Param val="(0, 0)" valType="list" updates="constant" name="pos"/>
        <Param val="True" valType="bool" updates="None" name="saveStartStop"/>
        <Param val="(800,600)" valType="list" updates="constant" name="size"/>
        <Param val="" valType="code" updates="None" name="startEstim"/>
        <Param val="time (s)" valType="str" updates="None" name="startType"/>
        <Param val="0" valType="code" updates="None" name="startVal"/>
        <Param val="duration (s)" valType="str" updates="None" name="stopType"/>
        <Param val="3" valType="code" updates="constant" name="stopVal"/>
        <Param val="True" valType="bool" updates="constant" name="stopWithRoutine"/>
        <Param val="True" valType="bool" updates="None" name="syncScreenRefresh"/>
        <Param val="pix" valType="str" updates="None" name="units"/>
        <Param val="" valType="code" updates="None" name="validator"/>
        <Param val="1" valType="num" updates="None" name="volume"/>
      </MovieComponent>
      <CodeComponent name="elTrial" plugin="None">
        <Param val="# This Before Experiment tab of the elTrial code component creates a folder&amp;#10;# to log video frame messages for Data Viewer drawing&amp;#10;&amp;#10;# create a 'graphics' folder to save the VFRAME commands for each trial&amp;#10;graphics_folder = os.path.join(session_folder, 'graphics')&amp;#10;if not os.path.exists(graphics_folder):&amp;#10;    os.makedirs(graphics_folder)&amp;#10;&amp;#10;eyelinkThisFrameCallOnFlipScheduled = False&amp;#10;zeroTimeDLF = 0.0&amp;#10;sentDrawListMessage = False&amp;#10;frame_index = -1&amp;#10;&amp;#10;&amp;#10;# This method, created by the EyeLink MarkEvents component code, will get called to handle&amp;#10;# sending event marking messages, logging Data Viewer (DV) stimulus drawing info, logging DV interest area info,&amp;#10;# sending DV Target Position Messages, and/or logging DV video frame marking info=information&amp;#10;def eyelink_onFlip_MarkEvents(movie,globalClock,scn_width,scn_height,dlf,dlf_file):&amp;#10;    global eyelinkThisFrameCallOnFlipScheduled,eyelinkLastFlipTime,zeroTimeDLF,sentDrawListMessage&amp;#10;&amp;#10;    # Log the time of the current frame onset for upcoming messaging/event logging&amp;#10;    currentFrameTime = float(globalClock.getTime())&amp;#10;&amp;#10;    # Go through all stimulus components that need to be checked (for event marking,&amp;#10;    # DV drawing, and/or interest area logging) to see if any have just ONSET&amp;#10;&amp;#10;    if movie.tStartRefresh is not None and not movie.elOnsetDetected:&amp;#10;&amp;#10;        el_tracker.sendMessage('%s MOVIE_ONSET' % (int(round((globalClock.getTime()-movie.tStartRefresh)*1000))))&amp;#10;        el_tracker.sendMessage('!V TRIAL_VAR MOVIE_ONSET_TIME %i' % (movie.tStartRefresh*1000))&amp;#10;        &amp;#10;&amp;#10;        # If this is the first interest area instance of the trial write a message pointing&amp;#10;        # Data Viewer to the IAS file.  The time of the message will serve as the zero time point&amp;#10;        # for interest area information (e.g., instance start/end times) that is logged to the file&amp;#10;        if not movie.firstFramePresented:&amp;#10;            # send an IAREA FILE command message to let Data Viewer know where&amp;#10;            # to find the IAS file to load interest area information&amp;#10;            zeroTimeDLF = float(currentFrameTime)&amp;#10;            # send a DRAW_LIST command message to let Data Viewer know where&amp;#10;            # to find the drawing messages to correctly present the stimuli&amp;#10;            el_tracker.sendMessage('%s !V DRAW_LIST graphics/%s' % (int(round((globalClock.getTime()-currentFrameTime)*1000)),dlf))&amp;#10;            dlf_file.write('0 CLEAR 128 128 128\n')&amp;#10;            sentDrawListMessage = True&amp;#10;&amp;#10;            # log that the movie onset has been detected&amp;#10;            movie.elOnsetDetected = True&amp;#10;&amp;#10;&amp;#10;    # Send movie frame event messages and video frame draw messages for Data Viewer&amp;#10;    # integration.  # See the Data Viewer User Manual, section&amp;#10;    # Protocol for EyeLink Data to Viewer Integration -&gt; Video Commands&amp;#10; &amp;#10;    #Check whether the movie playback has begun yet&amp;#10;    if movie.tStartRefresh is not None:&amp;#10;        # A flag to tell us whether we need to send a video frame onset message during &amp;#10;        # the current Psychopy screen refresh&amp;#10;        sendFrameMessage = 0&amp;#10;        # Check the movie class type to identify the frame identifier&amp;#10;        # MovieStim3 does not provide the frame index, so we need to determine&amp;#10;        # it manually based on the value of the current frame time&amp;#10;        if &quot;MovieStim3&quot; in str(movie.__class__):&amp;#10;            # MovieStim3 does not report the frame number or index, but does provide the &amp;#10;            # frame onset time for the current frame. We can use this to identify each&amp;#10;            # frame increase&amp;#10;            vidFrameTime = movie.getCurrentFrameTime()&amp;#10;            # Wait until the video has begun and define frame_index&amp;#10;            if not movie.firstFramePresented:&amp;#10;                # reset frame_index to 0&amp;#10;                movie.elMarkingFrameIndex = 0&amp;#10;                # log that we will have sent the video onset marking message&amp;#10;                # for future iterations/frames&amp;#10;                movie.firstFramePresented = True&amp;#10;                # log that we need to send a message marking the current frame onset&amp;#10;                sendFrameMessage = 1&amp;#10;            # check whether we have started playback and are on a new frame and if &amp;#10;            # so then update our variables&amp;#10;            if movie.elMarkingFrameIndex &gt;= 0 and vidFrameTime &gt; movie.previousFrameTime:&amp;#10;                movie.elMarkingFrameIndex = movie.elMarkingFrameIndex + 1&amp;#10;                sendFrameMessage = 1&amp;#10;                movie.previousFrameTime = vidFrameTime&amp;#10;        # else if we are using a movie stim class that provides the current&amp;#10;        # frame number then we can grab the frame number directly&amp;#10;        else:&amp;#10;            if not movie.firstFramePresented:&amp;#10;                # log that we will have sent the video onset marking message&amp;#10;                # for future iterations/frames&amp;#10;                movie.firstFramePresented = True&amp;#10;            # Other movie players have access to the frame number&amp;#10;            frameNum = movie.getCurrentFrameNumber()&amp;#10;            vidFrameTime = currentFrameTime&amp;#10;            # If we have a new frame then update the frame number and log&amp;#10;            # that we need to send a frame-marking message&amp;#10;            if frameNum &gt;= 0 and frameNum is not movie.elMarkingFrameIndex:&amp;#10;                movie.elMarkingFrameIndex = frameNum&amp;#10;                sendFrameMessage = 1&amp;#10;        # If we need to mark a frame onset, then with the above frame_index and &amp;#10;        # currentTime variables defined, update the times, frame level messages and &amp;#10;        # interest are information&amp;#10;        if sendFrameMessage == 1:&amp;#10;            # send a message to mark the onset of each frame&amp;#10;            el_tracker.sendMessage('%s Movie_Frame %d' % (int(round((globalClock.getTime()-vidFrameTime)*1000)),movie.elMarkingFrameIndex))&amp;#10;            # Write a VFRAME message to mark the onset of each frame&amp;#10;            # Format: VFRAME frame_num pos_x, pos_y, path_to_file&amp;#10;            # See the Data Viewer User Manual, section:&amp;#10;            # Protocol for EyeLink Data to Viewer Integration -&gt; Video Commands&amp;#10;            if &quot;MovieStim3&quot; in str(movie.__class__):&amp;#10;                vidFrameTime = currentFrameTime&amp;#10;                dlf_file.write('%i VFRAME %d %d %d ../../%s\n' % (int(round((zeroTimeDLF - vidFrameTime)*1000+3)),&amp;#10;                    movie.elMarkingFrameIndex,&amp;#10;                  int(round(movie.elPos[0]-movie.elSize[0]/2.0)),&amp;#10;                  int(round(movie.elPos[1]-movie.elSize[1]/2.0)),&amp;#10;                  movie.filename))&amp;#10;            else:&amp;#10;                dlf_file.write('%i VFRAME %d %d %d ../../%s\n' % (int(round((zeroTimeDLF - vidFrameTime)*1000+3)),&amp;#10;                    movie.elMarkingFrameIndex,&amp;#10;                  int(round(movie.elPos[0]-movie.elSize[0]/2.0)),&amp;#10;                  int(round(movie.elPos[1]-movie.elSize[1]/2.0)),&amp;#10;                  movie.filename))&amp;#10;            # Log that we don't need to send a new frame message again&amp;#10;            # until a new frame is actually drawn/detected&amp;#10;            sendFrameMessage = 0&amp;#10;            &amp;#10;    # This logs whether a call to this method has already been scheduled for the upcoming retrace&amp;#10;    # And is used to help ensure we schedule only one callOnFlip call for each retrace&amp;#10;    eyelinkThisFrameCallOnFlipScheduled = False&amp;#10;&amp;#10;&amp;#10;# this method converts PsychoPy position values to EyeLink position values&amp;#10;# EyeLink position values are in pixel units and are such that 0,0 corresponds &amp;#10;# to the top-left corner of the screen and increase as position moves right/down&amp;#10;def eyelink_pos(pos,winSize):&amp;#10;    screenUnitType = 'pix'&amp;#10;    scn_width,scn_height = winSize&amp;#10;    if screenUnitType == 'pix':&amp;#10;        elPos = [pos[0] + scn_width/2,scn_height/2 - pos[1]]&amp;#10;    elif screenUnitType == 'height':&amp;#10;        elPos = [scn_width/2 + pos[0] * scn_height,scn_height/2 + pos[1] * scn_height]&amp;#10;    elif screenUnitType == &quot;norm&quot;:&amp;#10;        elPos = [(scn_width/2 * pos[0]) + scn_width/2,scn_height/2 + pos[1] * scn_height]&amp;#10;    else:&amp;#10;        print(&quot;ERROR:  Only pixel, height, and norm units supported for conversion to EyeLink position units&quot;)&amp;#10;    return [int(round(elPos[0])),int(round(elPos[1]))]&amp;#10;&amp;#10;# this method converts PsychoPy size values to EyeLink size values&amp;#10;# EyeLink size values are in pixels&amp;#10;def eyelink_size(size,winSize):&amp;#10;    screenUnitType = 'pix'&amp;#10;    scn_width,scn_height = winSize&amp;#10;    if len(size) == 1:&amp;#10;        size = [size[0],size[0]]&amp;#10;    if screenUnitType == 'pix':&amp;#10;        elSize = [size[0],size[1]]&amp;#10;    elif screenUnitType == 'height':&amp;#10;        elSize = [int(round(scn_height*size[0])),int(round(scn_height*size[1]))]&amp;#10;    elif screenUnitType == &quot;norm&quot;:&amp;#10;        elSize = [size[0]/2 * scn_width,size[1]/2 * scn_height]&amp;#10;    else:&amp;#10;        print(&quot;ERROR:  Only pixel, height, and norm units supported for conversion to EyeLink position units&quot;)&amp;#10;    return [int(round(elSize[0])),int(round(elSize[1]))]&amp;#10;" valType="extendedCode" updates="constant" name="Before Experiment"/>
        <Param val="/* Syntax Error: Fix Python code */" valType="extendedCode" updates="constant" name="Before JS Experiment"/>
        <Param val="# This Begin Experiment tab of the elTrial component just initializes&amp;#10;# a trial counter variable at the beginning of the experiment&amp;#10;&amp;#10;trial_index = 1&amp;#10;&amp;#10;# import a module to help with video frame logging&amp;#10;import math&amp;#10;&amp;#10;global eyelinkThisFrameCallOnFlipScheduled,eyelinkLastFlipTime,zeroTimeDLF,sentDrawListMessage&amp;#10;" valType="extendedCode" updates="constant" name="Begin Experiment"/>
        <Param val="" valType="extendedCode" updates="constant" name="Begin JS Experiment"/>
        <Param val="" valType="extendedCode" updates="constant" name="Begin JS Routine"/>
        <Param val="# This Begin Routine tab of the elTrial component resets some &amp;#10;# variables that are used to keep track of whether certain trial events have&amp;#10;# happened, sends trial variable messages to the EDF to mark condition&amp;#10;# information, and opens a DRAW LIST file which will be used to log the times&amp;#10;# of each video frame onset so that the video can be seen in Data Viewer's&amp;#10;# Trial View window.&amp;#10;&amp;#10;# these variables keep track of whether the intial video onset and current frame&amp;#10;# presentation have occured yet (0 = no, 1 = yes) and to keep track of the &amp;#10;# the current frame number.  They later help us to ensure that each event &amp;#10;# marking message only gets sent once, at the time of each event&amp;#10;&amp;#10;# This logs whether a call to this method has already been scheduled for the upcoming retrace&amp;#10;# And is used to help ensure we schedule only one callOnFlip call for each retrace&amp;#10;eyelinkThisFrameCallOnFlipScheduled = False&amp;#10;&amp;#10;# A simple flag to keep track of whether the movie has onset yet&amp;#10;movie.elOnsetDetected = False&amp;#10;# Get the position and size of the movie component in EyeLink units&amp;#10;movie.elPos = eyelink_pos(movie.pos,[scn_width,scn_height])&amp;#10;movie.elSize = eyelink_size(movie.size,[scn_width,scn_height])&amp;#10;# Stores the time of the previous frame -- used when checking for new frames&amp;#10;movie.previousFrameTime = 0&amp;#10;# The frame count begins at -1, when the video is not playing&amp;#10;movie.elMarkingFrameIndex = -1&amp;#10;# Boolean that logs wheter the first frame of the movie has been presented yet&amp;#10;movie.firstFramePresented = False&amp;#10;&amp;#10;# create a keyboard instance and reinitialize a kePressNameList, which &amp;#10;# will store list of key names currently being pressed (to allow Ctrl-C abort)&amp;#10;kb = keyboard.Keyboard()&amp;#10;keyPressNameList = list()&amp;#10;&amp;#10;# send a &quot;TRIALID&quot; message to mark the start of a trial, see Data&amp;#10;# Viewer User Manual, &quot;Protocol for EyeLink Data to Viewer Integration&quot;&amp;#10;el_tracker.sendMessage('!V TRIAL_VAR video %s' % video)&amp;#10;el_tracker.sendMessage('!V TRIAL_VAR condition %s' % condition)&amp;#10;&amp;#10;# clear the Data Viewer screen as well&amp;#10;bgcolor_RGB = (180, 180, 180)&amp;#10;el_tracker.sendMessage('!V CLEAR %d %d %d' % bgcolor_RGB)&amp;#10;&amp;#10;# open a DRAW LIST file to save the frame timing info for the video, which will&amp;#10;# help us to be able to see the video in Data Viewer's Trial View window&amp;#10;dlf = 'VC_%d.dlf' % trial_index&amp;#10;dlf_file = open(os.path.join(graphics_folder, dlf), 'w')&amp;#10;path_to_vid = os.path.join('../../videos', video)&amp;#10;&amp;#10;routineTimer.reset()" valType="extendedCode" updates="constant" name="Begin Routine"/>
        <Param val="Py" valType="str" updates="None" name="Code Type"/>
        <Param val="## This Each Frame tab of the elTrial component handles method calls to mark&amp;#10;## experimental events via messages to the EDF, send additional messages&amp;#10;## to allow visualization of trial stimuli in Data Viewer, log trial variable&amp;#10;## information associated with stimulus timing&amp;#10;## This Each Frame section also checks whether the eye tracker is still properly &amp;#10;## recording (and aborts the trial if not) and whether a quit key has been pressed&amp;#10;&amp;#10;&amp;#10;# Check to see if we have already scheduled to called eyelink_onFlip_MarkEvents &amp;#10;# for the upcoming screen flip/retrace. We essentially schedule a call of &amp;#10;# eyelink_onFlip_MarkEvents for each screen flip/retrace&amp;#10;if not eyelinkThisFrameCallOnFlipScheduled:&amp;#10;&amp;#10;    # Schedule a call of the eyelink_onFlip_MarkEvents method for the upcoming&amp;#10;    # screen retrace.  &amp;#10;    # eyelink_onFlip_MarkEvents is a method defined in the Before Experiment tab&amp;#10;    # that checks for component onsets and sends event marking messgaes and Data Viewer&amp;#10;    # integration messages when these component onsets are detected&amp;#10;    win.callOnFlip(eyelink_onFlip_MarkEvents,movie,globalClock,scn_width,scn_height,dlf,dlf_file)&amp;#10;    &amp;#10;    # Set this variable to True so that we don't schedule another call to this&amp;#10;    # method again before the flip/retrace happens&amp;#10;    eyelinkThisFrameCallOnFlipScheduled = True&amp;#10;&amp;#10;# check keyboard events and then check to see if abort key combination (Ctrl-C) pressed&amp;#10;keyPressList = kb.getKeys(keyList = ['lctrl','rctrl','c'], waitRelease = False, clear = False)&amp;#10;for keyPress in keyPressList:&amp;#10;    keyPressName = keyPress.name&amp;#10;    if keyPressName not in keyPressNameList:&amp;#10;        keyPressNameList.append(keyPress.name)&amp;#10;if ('lctrl' in keyPressNameList or 'rctrl' in keyPressNameList) and 'c' in keyPressNameList:&amp;#10;    el_tracker.sendMessage('terminated_by_user')&amp;#10;    terminate_task(genv,edf_file,session_folder,session_identifier)&amp;#10;#check for key releases&amp;#10;keyReleaseList = kb.getKeys(keyList = ['lctrl','rctrl','c'], waitRelease = True, clear = False)&amp;#10;for keyRelease in keyReleaseList:&amp;#10;    keyReleaseName = keyRelease.name&amp;#10;    if keyReleaseName in keyPressNameList:&amp;#10;        keyPressNameList.remove(keyReleaseName)&amp;#10;" valType="extendedCode" updates="constant" name="Each Frame"/>
        <Param val="" valType="extendedCode" updates="constant" name="Each JS Frame"/>
        <Param val="" valType="extendedCode" updates="constant" name="End Experiment"/>
        <Param val="" valType="extendedCode" updates="constant" name="End JS Experiment"/>
        <Param val="" valType="extendedCode" updates="constant" name="End JS Routine"/>
        <Param val="" valType="extendedCode" updates="constant" name="End Routine"/>
        <Param val="False" valType="bool" updates="None" name="disabled"/>
        <Param val="elTrial" valType="code" updates="None" name="name"/>
      </CodeComponent>
    </Routine>
    <Routine name="instruct">
      <RoutineSettingsComponent name="instruct" plugin="None">
        <Param val="none" valType="str" updates="None" name="backgroundFit"/>
        <Param val="" valType="str" updates="None" name="backgroundImg"/>
        <Param val="$[0,0,0]" valType="color" updates="None" name="color"/>
        <Param val="rgb" valType="str" updates="None" name="colorSpace"/>
        <Param val="" valType="str" updates="constant" name="desc"/>
        <Param val="False" valType="bool" updates="None" name="disabled"/>
        <Param val="" valType="code" updates="None" name="durationEstim"/>
        <Param val="False" valType="code" updates="None" name="forceNonSlip"/>
        <Param val="instruct" valType="code" updates="None" name="name"/>
        <Param val="True" valType="bool" updates="None" name="saveStartStop"/>
        <Param val="" valType="code" updates="constant" name="skipIf"/>
        <Param val="duration (s)" valType="str" updates="None" name="stopType"/>
        <Param val="" valType="code" updates="constant" name="stopVal"/>
        <Param val="False" valType="bool" updates="None" name="useWindowParams"/>
      </RoutineSettingsComponent>
      <TextComponent name="taskInstructions" plugin="None">
        <Param val="white" valType="color" updates="constant" name="color"/>
        <Param val="rgb" valType="str" updates="constant" name="colorSpace"/>
        <Param val="1" valType="num" updates="constant" name="contrast"/>
        <Param val="False" valType="bool" updates="None" name="disabled"/>
        <Param val="False" valType="code" updates="constant" name="draggable"/>
        <Param val="" valType="num" updates="None" name="durationEstim"/>
        <Param val="None" valType="str" updates="constant" name="flip"/>
        <Param val="Open Sans" valType="str" updates="constant" name="font"/>
        <Param val="LTR" valType="str" updates="None" name="languageStyle"/>
        <Param val="50" valType="num" updates="constant" name="letterHeight"/>
        <Param val="taskInstructions" valType="code" updates="None" name="name"/>
        <Param val="" valType="num" updates="constant" name="opacity"/>
        <Param val="0" valType="num" updates="constant" name="ori"/>
        <Param val="(0, 0)" valType="list" updates="constant" name="pos"/>
        <Param val="True" valType="bool" updates="None" name="saveStartStop"/>
        <Param val="" valType="num" updates="None" name="startEstim"/>
        <Param val="time (s)" valType="str" updates="None" name="startType"/>
        <Param val="0" valType="num" updates="None" name="startVal"/>
        <Param val="duration (s)" valType="str" updates="None" name="stopType"/>
        <Param val="" valType="num" updates="constant" name="stopVal"/>
        <Param val="True" valType="bool" updates="None" name="syncScreenRefresh"/>
        <Param val="Press any key to start the videos" valType="str" updates="constant" name="text"/>
        <Param val="from exp settings" valType="str" updates="None" name="units"/>
        <Param val="" valType="code" updates="None" name="validator"/>
        <Param val="" valType="num" updates="constant" name="wrapWidth"/>
      </TextComponent>
      <KeyboardComponent name="ready" plugin="None">
        <Param val="" valType="list" updates="constant" name="allowedKeys"/>
        <Param val="thisTrial.corrAns" valType="str" updates="constant" name="correctAns"/>
        <Param val="" valType="str" updates="None" name="deviceLabel"/>
        <Param val="False" valType="bool" updates="None" name="disabled"/>
        <Param val="True" valType="bool" updates="constant" name="discard previous"/>
        <Param val="" valType="code" updates="None" name="durationEstim"/>
        <Param val="True" valType="bool" updates="constant" name="forceEndRoutine"/>
        <Param val="ready" valType="code" updates="None" name="name"/>
        <Param val="press" valType="str" updates="constant" name="registerOn"/>
        <Param val="True" valType="bool" updates="None" name="saveStartStop"/>
        <Param val="" valType="code" updates="None" name="startEstim"/>
        <Param val="time (s)" valType="str" updates="None" name="startType"/>
        <Param val="0" valType="code" updates="None" name="startVal"/>
        <Param val="duration (s)" valType="str" updates="None" name="stopType"/>
        <Param val="" valType="code" updates="constant" name="stopVal"/>
        <Param val="nothing" valType="str" updates="constant" name="store"/>
        <Param val="False" valType="bool" updates="constant" name="storeCorrect"/>
        <Param val="True" valType="bool" updates="constant" name="syncScreenRefresh"/>
      </KeyboardComponent>
    </Routine>
    <Routine name="thanks">
      <RoutineSettingsComponent name="thanks" plugin="None">
        <Param val="none" valType="str" updates="None" name="backgroundFit"/>
        <Param val="" valType="str" updates="None" name="backgroundImg"/>
        <Param val="$[0,0,0]" valType="color" updates="None" name="color"/>
        <Param val="rgb" valType="str" updates="None" name="colorSpace"/>
        <Param val="" valType="str" updates="constant" name="desc"/>
        <Param val="False" valType="bool" updates="None" name="disabled"/>
        <Param val="" valType="code" updates="None" name="durationEstim"/>
        <Param val="False" valType="code" updates="None" name="forceNonSlip"/>
        <Param val="thanks" valType="code" updates="None" name="name"/>
        <Param val="True" valType="bool" updates="None" name="saveStartStop"/>
        <Param val="" valType="code" updates="constant" name="skipIf"/>
        <Param val="duration (s)" valType="str" updates="None" name="stopType"/>
        <Param val="" valType="code" updates="constant" name="stopVal"/>
        <Param val="False" valType="bool" updates="None" name="useWindowParams"/>
      </RoutineSettingsComponent>
      <TextComponent name="endScreen" plugin="None">
        <Param val="white" valType="color" updates="constant" name="color"/>
        <Param val="rgb" valType="str" updates="constant" name="colorSpace"/>
        <Param val="1" valType="num" updates="constant" name="contrast"/>
        <Param val="False" valType="bool" updates="None" name="disabled"/>
        <Param val="False" valType="code" updates="constant" name="draggable"/>
        <Param val="" valType="code" updates="None" name="durationEstim"/>
        <Param val="None" valType="str" updates="constant" name="flip"/>
        <Param val="Open Sans" valType="str" updates="constant" name="font"/>
        <Param val="LTR" valType="str" updates="None" name="languageStyle"/>
        <Param val="50" valType="num" updates="constant" name="letterHeight"/>
        <Param val="endScreen" valType="code" updates="None" name="name"/>
        <Param val="" valType="num" updates="constant" name="opacity"/>
        <Param val="0" valType="num" updates="constant" name="ori"/>
        <Param val="(0, 0)" valType="list" updates="constant" name="pos"/>
        <Param val="True" valType="bool" updates="None" name="saveStartStop"/>
        <Param val="" valType="code" updates="None" name="startEstim"/>
        <Param val="time (s)" valType="str" updates="None" name="startType"/>
        <Param val="0.0" valType="code" updates="None" name="startVal"/>
        <Param val="duration (s)" valType="str" updates="None" name="stopType"/>
        <Param val="2.0" valType="code" updates="constant" name="stopVal"/>
        <Param val="True" valType="bool" updates="None" name="syncScreenRefresh"/>
        <Param val="This is the end of the experiment.&amp;#10;&amp;#10;Thanks!" valType="str" updates="constant" name="text"/>
        <Param val="from exp settings" valType="str" updates="None" name="units"/>
        <Param val="" valType="code" updates="None" name="validator"/>
        <Param val="" valType="num" updates="constant" name="wrapWidth"/>
      </TextComponent>
    </Routine>
    <Routine name="eyelinkSetup">
      <RoutineSettingsComponent name="eyelinkSetup" plugin="None">
        <Param val="none" valType="str" updates="None" name="backgroundFit"/>
        <Param val="" valType="str" updates="None" name="backgroundImg"/>
        <Param val="$[0,0,0]" valType="color" updates="None" name="color"/>
        <Param val="rgb" valType="str" updates="None" name="colorSpace"/>
        <Param val="" valType="str" updates="constant" name="desc"/>
        <Param val="False" valType="bool" updates="None" name="disabled"/>
        <Param val="" valType="code" updates="None" name="durationEstim"/>
        <Param val="False" valType="code" updates="None" name="forceNonSlip"/>
        <Param val="eyelinkSetup" valType="code" updates="None" name="name"/>
        <Param val="True" valType="bool" updates="None" name="saveStartStop"/>
        <Param val="" valType="code" updates="constant" name="skipIf"/>
        <Param val="duration (s)" valType="str" updates="None" name="stopType"/>
        <Param val="" valType="code" updates="constant" name="stopVal"/>
        <Param val="False" valType="bool" updates="None" name="useWindowParams"/>
      </RoutineSettingsComponent>
      <TextComponent name="elInstructions" plugin="None">
        <Param val="white" valType="color" updates="constant" name="color"/>
        <Param val="rgb" valType="str" updates="constant" name="colorSpace"/>
        <Param val="1" valType="num" updates="constant" name="contrast"/>
        <Param val="False" valType="bool" updates="None" name="disabled"/>
        <Param val="False" valType="code" updates="constant" name="draggable"/>
        <Param val="" valType="num" updates="None" name="durationEstim"/>
        <Param val="None" valType="str" updates="constant" name="flip"/>
        <Param val="Open Sans" valType="str" updates="constant" name="font"/>
        <Param val="LTR" valType="str" updates="None" name="languageStyle"/>
        <Param val="50" valType="num" updates="constant" name="letterHeight"/>
        <Param val="elInstructions" valType="code" updates="None" name="name"/>
        <Param val="" valType="num" updates="constant" name="opacity"/>
        <Param val="0" valType="num" updates="constant" name="ori"/>
        <Param val="(0, 0)" valType="list" updates="constant" name="pos"/>
        <Param val="True" valType="bool" updates="None" name="saveStartStop"/>
        <Param val="" valType="num" updates="None" name="startEstim"/>
        <Param val="time (s)" valType="str" updates="None" name="startType"/>
        <Param val="0" valType="num" updates="None" name="startVal"/>
        <Param val="duration (s)" valType="str" updates="None" name="stopType"/>
        <Param val="" valType="num" updates="constant" name="stopVal"/>
        <Param val="True" valType="bool" updates="None" name="syncScreenRefresh"/>
        <Param val="Press any key to start Camera Setup" valType="str" updates="constant" name="text"/>
        <Param val="from exp settings" valType="str" updates="None" name="units"/>
        <Param val="" valType="code" updates="None" name="validator"/>
        <Param val="" valType="num" updates="constant" name="wrapWidth"/>
      </TextComponent>
      <KeyboardComponent name="key_resp" plugin="None">
        <Param val="" valType="list" updates="constant" name="allowedKeys"/>
        <Param val="" valType="str" updates="constant" name="correctAns"/>
        <Param val="" valType="str" updates="None" name="deviceLabel"/>
        <Param val="False" valType="bool" updates="None" name="disabled"/>
        <Param val="True" valType="bool" updates="constant" name="discard previous"/>
        <Param val="" valType="num" updates="None" name="durationEstim"/>
        <Param val="True" valType="bool" updates="constant" name="forceEndRoutine"/>
        <Param val="key_resp" valType="code" updates="None" name="name"/>
        <Param val="press" valType="str" updates="constant" name="registerOn"/>
        <Param val="True" valType="bool" updates="None" name="saveStartStop"/>
        <Param val="" valType="num" updates="None" name="startEstim"/>
        <Param val="time (s)" valType="str" updates="None" name="startType"/>
        <Param val="0" valType="num" updates="None" name="startVal"/>
        <Param val="duration (s)" valType="str" updates="None" name="stopType"/>
        <Param val="" valType="num" updates="constant" name="stopVal"/>
        <Param val="last key" valType="str" updates="constant" name="store"/>
        <Param val="False" valType="bool" updates="constant" name="storeCorrect"/>
        <Param val="True" valType="bool" updates="constant" name="syncScreenRefresh"/>
      </KeyboardComponent>
      <CodeComponent name="elConnect" plugin="None">
        <Param val="# DESCRIPTION:&amp;#10;# This is a basic example, which shows how connect to and disconnect from&amp;#10;# the tracker, how to open and close data file, how to start/stop recording,&amp;#10;# and the standard messages for integration with Data Viewer.&amp;#10;# Each trial presents a fixation cross for 0.5 seconds followed by an image&amp;#10;# for 4 seconds.  A keyboard press terminates the trial.&amp;#10;&amp;#10;# The code components in the eyelinkSetup, eyelinkStartRecording, trial, and &amp;#10;# eyelinkStopRecording routines handle communication with the Host PC/EyeLink&amp;#10;# system.  All the code components are set to Code Type Py, and each code &amp;#10;# component may have code in the various tabs (e.g., Before Experiment, Begin&amp;#10;# Experiment, etc.)&amp;#10;&amp;#10;# Last updated: September 20, 2024&amp;#10;&amp;#10;# This Before Experiment tab of the elConnect component imports some&amp;#10;# modules we need, manages data filenames, allows for dummy mode configuration&amp;#10;# (for testing), connects to the Host PC, and defines some helper function &amp;#10;# definitions (which are called later)&amp;#10;&amp;#10;import pylink&amp;#10;import time&amp;#10;import platform&amp;#10;from PIL import Image  # for preparing the Host backdrop image&amp;#10;from EyeLinkCoreGraphicsPsychoPy import EyeLinkCoreGraphicsPsychoPy&amp;#10;from string import ascii_letters, digits&amp;#10;from psychopy import gui&amp;#10;&amp;#10;# Switch to the script folder&amp;#10;script_path = os.path.dirname(sys.argv[0])&amp;#10;if len(script_path) != 0:&amp;#10;    os.chdir(script_path)&amp;#10;&amp;#10;# Set this variable to True if you use the built-in retina screen as your&amp;#10;# primary display device on macOS. If have an external monitor, set this&amp;#10;# variable True if you choose to &quot;Optimize for Built-in Retina Display&quot;&amp;#10;# in the Displays preference settings.&amp;#10;use_retina = False&amp;#10;&amp;#10;# Set this variable to True to run the script in &quot;Dummy Mode&quot;&amp;#10;dummy_mode = False&amp;#10;&amp;#10;# Set up EDF data file name and local data folder&amp;#10;#&amp;#10;# The EDF data filename should not exceed 8 alphanumeric characters&amp;#10;# use ONLY number 0-9, letters, &amp; _ (underscore) in the filename&amp;#10;edf_fname = 'TEST'&amp;#10;&amp;#10;# Prompt user to specify an EDF data filename&amp;#10;# before we open a fullscreen window&amp;#10;dlg_title = &quot;Enter EDF Filename&quot;&amp;#10;dlg_prompt = &quot;Please enter a file name with 8 or fewer characters [letters, numbers, and underscore].&quot;&amp;#10;# loop until we get a valid filename&amp;#10;while True:&amp;#10;    dlg = gui.Dlg(dlg_title)&amp;#10;    dlg.addText(dlg_prompt)&amp;#10;    dlg.addField(&quot;Filename&quot;, &quot;EDF Filename:&quot;,&quot;Test&quot;)&amp;#10;    # show dialog and wait for OK or Cancel&amp;#10;    ok_data = dlg.show()&amp;#10;    if dlg.OK:  # if ok_data is not None&amp;#10;        print(&quot;EDF data filename: {}&quot;.format(ok_data[&quot;Filename&quot;]))&amp;#10;    else:&amp;#10;        print(&quot;user cancelled&quot;)&amp;#10;        core.quit()&amp;#10;        sys.exit()&amp;#10;&amp;#10;    # get the string entered by the experimenter&amp;#10;    tmp_str = ok_data[&quot;Filename&quot;]&amp;#10;    # strip trailing characters, ignore the &quot;.edf&quot; extension&amp;#10;    edf_fname = tmp_str.rstrip().split(&quot;.&quot;)[0]&amp;#10;&amp;#10;    # check if the filename is valid (length &lt;= 8 &amp; no special char)&amp;#10;    allowed_char = ascii_letters + digits + &quot;_&quot;&amp;#10;    if not all([c in allowed_char for c in edf_fname]):&amp;#10;        print(&quot;ERROR: Invalid EDF filename&quot;)&amp;#10;    elif len(edf_fname) &gt; 8:&amp;#10;        print(&quot;ERROR: EDF filename should not exceed 8 characters&quot;)&amp;#10;    else:&amp;#10;        break&amp;#10;        &amp;#10;# Set up a folder to store the EDF data files and the associated resources&amp;#10;# e.g., files defining the interest areas used in each trial&amp;#10;results_folder = 'results'&amp;#10;if not os.path.exists(results_folder):&amp;#10;    os.makedirs(results_folder)&amp;#10;&amp;#10;# We download EDF data file from the EyeLink Host PC to the local hard&amp;#10;# drive at the end of each testing session, here we rename the EDF to&amp;#10;# include session start date/time&amp;#10;time_str = time.strftime(&quot;_%Y_%m_%d_%H_%M&quot;, time.localtime())&amp;#10;session_identifier = edf_fname + time_str&amp;#10;&amp;#10;# create a folder for the current testing session in the &quot;results&quot; folder&amp;#10;session_folder = os.path.join(results_folder, session_identifier)&amp;#10;if not os.path.exists(session_folder):&amp;#10;    os.makedirs(session_folder)&amp;#10;&amp;#10;# For macOS users check if they have a retina screen&amp;#10;if 'Darwin' in platform.system():&amp;#10;    dlg = gui.Dlg(&quot;Retina Screen?&quot;)&amp;#10;    dlg.addText(&quot;What type of screen will the experiment run on?&quot;)&amp;#10;    dlg.addField(&quot;Screen Type&quot;, choices=[&quot;High Resolution (Retina, 2k, 4k, 5k)&quot;, &quot;Standard Resolution (HD or lower)&quot;])&amp;#10;    # show dialog and wait for OK or Cancel&amp;#10;    ok_data = dlg.show()&amp;#10;    if dlg.OK:&amp;#10;        if dlg.data[&quot;Screen Type&quot;] == &quot;High Resolution (Retina, 2k, 4k, 5k)&quot;:  &amp;#10;            use_retina = True&amp;#10;        else:&amp;#10;            use_retina = False&amp;#10;    else:&amp;#10;        print('user cancelled')&amp;#10;        core.quit()&amp;#10;        sys.exit()&amp;#10;&amp;#10;# Step 1: Connect to the EyeLink Host PC&amp;#10;#&amp;#10;# The Host IP address, by default, is &quot;*********&quot;.&amp;#10;# the &quot;el_tracker&quot; objected created here can be accessed through the Pylink&amp;#10;# Set the Host PC address to &quot;None&quot; (without quotes) to run the script&amp;#10;# in &quot;Dummy Mode&quot;&amp;#10;if dummy_mode:&amp;#10;    el_tracker = pylink.EyeLink(None)&amp;#10;else:&amp;#10;    try:&amp;#10;        el_tracker = pylink.EyeLink(&quot;*********&quot;)&amp;#10;    except RuntimeError as error:&amp;#10;        dlg = gui.Dlg(&quot;Dummy Mode?&quot;)&amp;#10;        dlg.addText(&quot;Couldn't connect to tracker at ********* -- continue in Dummy Mode?&quot;)&amp;#10;        # show dialog and wait for OK or Cancel&amp;#10;        ok_data = dlg.show()&amp;#10;        if dlg.OK:  # if ok_data is not None&amp;#10;            dummy_mode = True&amp;#10;            el_tracker = pylink.EyeLink(None)&amp;#10;        else:&amp;#10;            print('user cancelled')&amp;#10;            core.quit()&amp;#10;            sys.exit()&amp;#10;&amp;#10;# Define some helper functions for screen drawing &amp;#10;# and exiting trials/sessions early&amp;#10;def clear_screen(win,genv):&amp;#10;    &quot;&quot;&quot; clear up the PsychoPy window&quot;&quot;&quot;&amp;#10;    win.fillColor = genv.getBackgroundColor()&amp;#10;    win.flip()&amp;#10;&amp;#10;def show_msg(win, genv, text, wait_for_keypress=True):&amp;#10;    &quot;&quot;&quot; Show task instructions on screen&quot;&quot;&quot;&amp;#10;    scn_width, scn_height = win.size&amp;#10;    msg = visual.TextStim(win, text,&amp;#10;                          color=genv.getForegroundColor(),&amp;#10;                          wrapWidth=scn_width/2)&amp;#10;    clear_screen(win,genv)&amp;#10;    msg.draw()&amp;#10;    win.flip()&amp;#10;&amp;#10;    # wait indefinitely, terminates upon any key press&amp;#10;    if wait_for_keypress:&amp;#10;        kb = keyboard.Keyboard()&amp;#10;        #keys = kb.getKeys(['Enter'], waitRelease=False)&amp;#10;        waitKeys = kb.waitKeys(keyList=None, waitRelease=True, clear=True)&amp;#10;        clear_screen(win,genv)&amp;#10;&amp;#10;def terminate_task(genv,edf_file,session_folder,session_identifier):&amp;#10;    &quot;&quot;&quot; Terminate the task gracefully and retrieve the EDF data file&amp;#10;    &quot;&quot;&quot;&amp;#10;    el_tracker = pylink.getEYELINK()&amp;#10;&amp;#10;    if el_tracker.isConnected():&amp;#10;        # Terminate the current trial first if the task terminated prematurely&amp;#10;        error = el_tracker.isRecording()&amp;#10;        if error == pylink.TRIAL_OK:&amp;#10;            abort_trial()&amp;#10;&amp;#10;        # Put tracker in Offline mode&amp;#10;        el_tracker.setOfflineMode()&amp;#10;&amp;#10;        # Clear the Host PC screen and wait for 500 ms&amp;#10;        el_tracker.sendCommand('clear_screen 0')&amp;#10;        pylink.msecDelay(500)&amp;#10;&amp;#10;        # Close the edf data file on the Host&amp;#10;        el_tracker.closeDataFile()&amp;#10;&amp;#10;        # Show a file transfer message on the screen&amp;#10;        msg = 'EDF data is transferring from EyeLink Host PC...'&amp;#10;        show_msg(win, genv, msg, wait_for_keypress=False)&amp;#10;&amp;#10;        # Download the EDF data file from the Host PC to a local data folder&amp;#10;        # parameters: source_file_on_the_host, destination_file_on_local_drive&amp;#10;        local_edf = os.path.join(session_folder, session_identifier + '.EDF')&amp;#10;        try:&amp;#10;            el_tracker.receiveDataFile(edf_file, local_edf)&amp;#10;        except RuntimeError as error:&amp;#10;            print('ERROR:', error)&amp;#10;&amp;#10;        # Close the link to the tracker.&amp;#10;        el_tracker.close()&amp;#10;&amp;#10;    # close the PsychoPy window&amp;#10;    win.close()&amp;#10;&amp;#10;    # quit PsychoPy&amp;#10;    core.quit()&amp;#10;    sys.exit()&amp;#10;&amp;#10;def abort_trial():&amp;#10;    &quot;&quot;&quot;Ends recording &quot;&quot;&quot;&amp;#10;    el_tracker = pylink.getEYELINK()&amp;#10;&amp;#10;    # Stop recording&amp;#10;    if el_tracker.isRecording():&amp;#10;        # add 100 ms to catch final trial events&amp;#10;        pylink.pumpDelay(100)&amp;#10;        el_tracker.stopRecording()&amp;#10;        &amp;#10;    # Send a message to clear the Data Viewer screen&amp;#10;    bgcolor_RGB = (128, 128, 128)&amp;#10;    el_tracker.sendMessage('!V CLEAR %d %d %d' % bgcolor_RGB)&amp;#10;&amp;#10;    # send a message to mark trial end&amp;#10;    el_tracker.sendMessage('TRIAL_RESULT %d' % pylink.TRIAL_ERROR)&amp;#10;&amp;#10;    return pylink.TRIAL_ERROR&amp;#10;" valType="extendedCode" updates="constant" name="Before Experiment"/>
        <Param val="/* Syntax Error: Fix Python code */" valType="extendedCode" updates="constant" name="Before JS Experiment"/>
        <Param val="# This Begin Experiment tab of the elConnect component opens the EDF, gets graphic &amp;#10;# information from Psychopy, configures some eye tracker settings, logs the screen &amp;#10;# resolution for Data Viewer via a DISPLAY_COORDS message, and configures a &amp;#10;# graphics environment for eye tracker setup/calibration&amp;#10;&amp;#10;el_tracker = pylink.getEYELINK()&amp;#10;# Step 2: Open an EDF data file on the Host PC&amp;#10;global edf_fname&amp;#10;edf_file = edf_fname + &quot;.EDF&quot;&amp;#10;try:&amp;#10;    el_tracker.openDataFile(edf_file)&amp;#10;except RuntimeError as err:&amp;#10;    print('ERROR:', err)&amp;#10;    # close the link if we have one open&amp;#10;    if el_tracker.isConnected():&amp;#10;        el_tracker.close()&amp;#10;    core.quit()&amp;#10;    sys.exit()&amp;#10;&amp;#10;# Add a header text to the EDF file to identify the current experiment name&amp;#10;# This is OPTIONAL. If your text starts with &quot;RECORDED BY &quot; it will be&amp;#10;# available in DataViewer's Inspector window by clicking&amp;#10;# the EDF session node in the top panel and looking for the &quot;Recorded By:&quot;&amp;#10;# field in the bottom panel of the Inspector.&amp;#10;preamble_text = 'RECORDED BY %s' % os.path.basename(__file__)&amp;#10;el_tracker.sendCommand(&quot;add_file_preamble_text '%s'&quot; % preamble_text)&amp;#10;&amp;#10;# Step 3: Configure the tracker&amp;#10;#&amp;#10;# Put the tracker in offline mode before we change tracking parameters&amp;#10;el_tracker.setOfflineMode()&amp;#10;&amp;#10;# Get the software version:  1-EyeLink I, 2-EyeLink II, 3/4-EyeLink 1000,&amp;#10;# 5-EyeLink 1000 Plus, 6-Portable DUO&amp;#10;eyelink_ver = 0  # set version to 0, in case running in Dummy mode&amp;#10;if not dummy_mode:&amp;#10;    vstr = el_tracker.getTrackerVersionString()&amp;#10;    eyelink_ver = int(vstr.split()[-1].split('.')[0])&amp;#10;    # print out some version info in the shell&amp;#10;    print('Running experiment on %s, version %d' % (vstr, eyelink_ver))&amp;#10;&amp;#10;# File and Link data control&amp;#10;# what eye events to save in the EDF file, include everything by default&amp;#10;file_event_flags = 'LEFT,RIGHT,FIXATION,SACCADE,BLINK,MESSAGE,BUTTON,INPUT'&amp;#10;# what eye events to make available over the link, include everything by default&amp;#10;link_event_flags = 'LEFT,RIGHT,FIXATION,SACCADE,BLINK,BUTTON,FIXUPDATE,INPUT'&amp;#10;# what sample data to save in the EDF data file and to make available&amp;#10;# over the link, include the 'HTARGET' flag to save head target sticker&amp;#10;# data for supported eye trackers&amp;#10;if eyelink_ver &gt; 3:&amp;#10;    file_sample_flags = 'LEFT,RIGHT,GAZE,HREF,RAW,AREA,HTARGET,GAZERES,BUTTON,STATUS,INPUT'&amp;#10;    link_sample_flags = 'LEFT,RIGHT,GAZE,GAZERES,AREA,HTARGET,STATUS,INPUT'&amp;#10;else:&amp;#10;    file_sample_flags = 'LEFT,RIGHT,GAZE,HREF,PUPIL,AREA,GAZERES,BUTTON,STATUS,INPUT'&amp;#10;    link_sample_flags = 'LEFT,RIGHT,GAZE,GAZERES,AREA,STATUS,INPUT'&amp;#10;el_tracker.sendCommand(&quot;file_event_filter = %s&quot; % file_event_flags)&amp;#10;el_tracker.sendCommand(&quot;file_sample_data = %s&quot; % file_sample_flags)&amp;#10;el_tracker.sendCommand(&quot;link_event_filter = %s&quot; % link_event_flags)&amp;#10;el_tracker.sendCommand(&quot;link_sample_data = %s&quot; % link_sample_flags)&amp;#10;&amp;#10;# Optional tracking parameters&amp;#10;# Sample rate, 250, 500, 1000, or 2000, check your tracker specification&amp;#10;# if eyelink_ver &gt; 2:&amp;#10;#     el_tracker.sendCommand(&quot;sample_rate 1000&quot;)&amp;#10;# Choose a calibration type, H3, HV3, HV5, HV13 (HV = horizontal/vertical),&amp;#10;el_tracker.sendCommand(&quot;calibration_type = HV9&quot;)&amp;#10;# Set a gamepad button to accept calibration/drift check target&amp;#10;# You need a supported gamepad/button box that is connected to the Host PC&amp;#10;el_tracker.sendCommand(&quot;button_function 5 'accept_target_fixation'&quot;)&amp;#10;&amp;#10;# get the native screen resolution used by PsychoPy&amp;#10;scn_width, scn_height = win.size&amp;#10;# resolution fix for Mac retina displays&amp;#10;if 'Darwin' in platform.system():&amp;#10;    if use_retina:&amp;#10;        scn_width = int(scn_width/2.0)&amp;#10;        scn_height = int(scn_height/2.0)&amp;#10;&amp;#10;# Pass the display pixel coordinates (left, top, right, bottom) to the tracker&amp;#10;# see the EyeLink Installation Guide, &quot;Customizing Screen Settings&quot;&amp;#10;el_coords = &quot;screen_pixel_coords = 0 0 %d %d&quot; % (scn_width - 1, scn_height - 1)&amp;#10;el_tracker.sendCommand(el_coords)&amp;#10;&amp;#10;# Write a DISPLAY_COORDS message to the EDF file&amp;#10;# Data Viewer needs this piece of info for proper visualization, see Data&amp;#10;# Viewer User Manual, &quot;Protocol for EyeLink Data to Viewer Integration&quot;&amp;#10;dv_coords = &quot;DISPLAY_COORDS  0 0 %d %d&quot; % (scn_width - 1, scn_height - 1)&amp;#10;el_tracker.sendMessage(dv_coords)  &amp;#10;    &amp;#10;# Configure a graphics environment (genv) for tracker calibration&amp;#10;# Set the optional DISABLE_AUDIO paramter to True, since we are using a video&amp;#10;# calibration target.  This ensures we use audio from the target file and&amp;#10;# not the default EyeLink audio&amp;#10;genv = EyeLinkCoreGraphicsPsychoPy(el_tracker, win, True)&amp;#10;print(genv)  # print out the version number of the CoreGraphics library&amp;#10;" valType="extendedCode" updates="constant" name="Begin Experiment"/>
        <Param val="" valType="extendedCode" updates="constant" name="Begin JS Experiment"/>
        <Param val="" valType="extendedCode" updates="constant" name="Begin JS Routine"/>
        <Param val="" valType="extendedCode" updates="constant" name="Begin Routine"/>
        <Param val="Py" valType="str" updates="None" name="Code Type"/>
        <Param val="" valType="extendedCode" updates="constant" name="Each Frame"/>
        <Param val="" valType="extendedCode" updates="constant" name="Each JS Frame"/>
        <Param val="# This End Experiment tab of the elConnect component calls the &amp;#10;# terminate_task helper function to get the EDF file and close the connection&amp;#10;# to the Host PC&amp;#10;&amp;#10;# Disconnect, download the EDF file, then terminate the task&amp;#10;terminate_task(genv,edf_file,session_folder,session_identifier)" valType="extendedCode" updates="constant" name="End Experiment"/>
        <Param val="" valType="extendedCode" updates="constant" name="End JS Experiment"/>
        <Param val="if ((! dummy_mode)) {&amp;#10;    try {&amp;#10;        el_tracker.doTrackerSetup();&amp;#10;    } catch(err) {&amp;#10;        if ((err instanceof RuntimeError)) {&amp;#10;            console.log(&quot;ERROR:&quot;, err);&amp;#10;            el_tracker.exitCalibration();&amp;#10;        } else {&amp;#10;            throw err;&amp;#10;        }&amp;#10;    }&amp;#10;}&amp;#10;" valType="extendedCode" updates="constant" name="End JS Routine"/>
        <Param val="# This End Routine tab of the elConnect component configures some&amp;#10;# graphics options for calibration, and then performs a camera setup&amp;#10;# so that you can set up the eye tracker and calibrate/validate the participant&amp;#10;&amp;#10;# Set background and foreground colors for the calibration target&amp;#10;# in PsychoPy, (-1, -1, -1)=black, (1, 1, 1)=white, (0, 0, 0)=mid-gray&amp;#10;foreground_color = (-1, -1, -1)&amp;#10;background_color = tuple(win.color)&amp;#10;genv.setCalibrationColors(foreground_color, background_color)&amp;#10;&amp;#10;# Set up the calibration target&amp;#10;#&amp;#10;# The target could be a &quot;circle&quot; (default), a &quot;picture&quot;, a &quot;movie&quot; clip,&amp;#10;# or a rotating &quot;spiral&quot;. To configure the type of calibration target, set&amp;#10;# genv.setTargetType to &quot;circle&quot;, &quot;picture&quot;, &quot;movie&quot;, or &quot;spiral&quot;, e.g.,&amp;#10;# genv.setTargetType('picture')&amp;#10;#&amp;#10;# Use genv.setMovieTarget() to set a &quot;movie&quot; target&amp;#10;# genv.setMovieTarget(os.path.join('videos', 'calibVid.mov'))&amp;#10;&amp;#10;# Use a picture as the calibration target&amp;#10;# genv.setTargetType('picture')&amp;#10;# genv.setPictureTarget(os.path.join('images', 'fixTarget.bmp'))&amp;#10;&amp;#10;# Set up the calibration target&amp;#10;#&amp;#10;# The target could be a &quot;circle&quot; (default), a &quot;picture&quot;, a &quot;movie&quot; clip,&amp;#10;# or a rotating &quot;spiral&quot;. To configure the type of calibration target, set&amp;#10;# genv.setTargetType to &quot;circle&quot;, &quot;picture&quot;, &quot;movie&quot;, or &quot;spiral&quot;, e.g.,&amp;#10;genv.setTargetType('movie')&amp;#10;#&amp;#10;# Use genv.setMovieTarget() to set a &quot;movie&quot; target&amp;#10;genv.setMovieTarget(os.path.join('videos', 'calibVid.mp4'))&amp;#10;&amp;#10;# Configure the size of the calibration target (in pixels)&amp;#10;# this option applies only to &quot;circle&quot;, &quot;spiral&quot;, and &quot;movie&quot; targets&amp;#10;genv.setTargetSize(150)&amp;#10;&amp;#10;# Beeps to play during calibration, validation and drift correction&amp;#10;# parameters: target, good, error&amp;#10;#     target -- sound to play when target moves&amp;#10;#     good -- sound to play on successful operation&amp;#10;#     error -- sound to play on failure or interruption&amp;#10;# Each parameter could be ''--default sound, 'off'--no sound, or a wav file&amp;#10;genv.setCalibrationSounds('', '', '')&amp;#10;&amp;#10;# resolution fix for macOS retina display issues&amp;#10;if use_retina:&amp;#10;    genv.fixMacRetinaDisplay()&amp;#10;&amp;#10;#clear the screen before we begin Camera Setup mode&amp;#10;clear_screen(win,genv)&amp;#10;&amp;#10;# Request Pylink to use the PsychoPy window we opened above for calibration&amp;#10;pylink.openGraphicsEx(genv)&amp;#10;&amp;#10;# Peform a Camera Setup (eye tracker calibration)&amp;#10;# skip this step if running the script in Dummy Mode&amp;#10;if not dummy_mode:&amp;#10;    try:&amp;#10;        el_tracker.doTrackerSetup()&amp;#10;    except RuntimeError as err:&amp;#10;        print('ERROR:', err)&amp;#10;        el_tracker.exitCalibration()&amp;#10;clear_screen(win,genv)&amp;#10;" valType="extendedCode" updates="constant" name="End Routine"/>
        <Param val="False" valType="bool" updates="None" name="disabled"/>
        <Param val="elConnect" valType="code" updates="None" name="name"/>
      </CodeComponent>
    </Routine>
    <Routine name="eyelinkStartRecording">
      <RoutineSettingsComponent name="eyelinkStartRecording" plugin="None">
        <Param val="none" valType="str" updates="None" name="backgroundFit"/>
        <Param val="" valType="str" updates="None" name="backgroundImg"/>
        <Param val="$[0,0,0]" valType="color" updates="None" name="color"/>
        <Param val="rgb" valType="str" updates="None" name="colorSpace"/>
        <Param val="" valType="str" updates="constant" name="desc"/>
        <Param val="False" valType="bool" updates="None" name="disabled"/>
        <Param val="" valType="code" updates="None" name="durationEstim"/>
        <Param val="False" valType="code" updates="None" name="forceNonSlip"/>
        <Param val="eyelinkStartRecording" valType="code" updates="None" name="name"/>
        <Param val="True" valType="bool" updates="None" name="saveStartStop"/>
        <Param val="" valType="code" updates="constant" name="skipIf"/>
        <Param val="duration (s)" valType="str" updates="None" name="stopType"/>
        <Param val="" valType="code" updates="constant" name="stopVal"/>
        <Param val="False" valType="bool" updates="None" name="useWindowParams"/>
      </RoutineSettingsComponent>
      <CodeComponent name="elStartRecord" plugin="None">
        <Param val="" valType="extendedCode" updates="constant" name="Before Experiment"/>
        <Param val="" valType="extendedCode" updates="constant" name="Before JS Experiment"/>
        <Param val="" valType="extendedCode" updates="constant" name="Begin Experiment"/>
        <Param val="" valType="extendedCode" updates="constant" name="Begin JS Experiment"/>
        <Param val="haveSentImageOnsetMessage = 0;&amp;#10;el_tracker = pylink.getEYELINK();&amp;#10;el_tracker.setOfflineMode();&amp;#10;el_tracker.sendCommand(&quot;clear_screen 0&quot;);&amp;#10;im = Image.open(((script_path + &quot;/&quot;) + trialImage));&amp;#10;im = im.resize([scn_width, scn_height]);&amp;#10;img_pixels = im.load();&amp;#10;pixels = function () {&amp;#10;    var _pj_a = [], _pj_b = util.range(scn_height);&amp;#10;    for (var _pj_c = 0, _pj_d = _pj_b.length; (_pj_c &lt; _pj_d); _pj_c += 1) {&amp;#10;        var j = _pj_b[_pj_c];&amp;#10;        _pj_a.push(function () {&amp;#10;    var _pj_e = [], _pj_f = util.range(scn_width);&amp;#10;    for (var _pj_g = 0, _pj_h = _pj_f.length; (_pj_g &lt; _pj_h); _pj_g += 1) {&amp;#10;        var i = _pj_f[_pj_g];&amp;#10;        _pj_e.push(img_pixels[[i, j]]);&amp;#10;    }&amp;#10;    return _pj_e;&amp;#10;}&amp;#10;.call(this));&amp;#10;    }&amp;#10;    return _pj_a;&amp;#10;}&amp;#10;.call(this);&amp;#10;el_tracker.bitmapBackdrop(scn_width, scn_height, pixels, 0, 0, scn_width, scn_height, 0, 0, pylink.BX_MAXCONTRAST);&amp;#10;left = (Number.parseInt((scn_width / 2.0)) - 60);&amp;#10;top = (Number.parseInt((scn_height / 2.0)) - 60);&amp;#10;right = (Number.parseInt((scn_width / 2.0)) + 60);&amp;#10;bottom = (Number.parseInt((scn_height / 2.0)) + 60);&amp;#10;draw_cmd = `draw_filled_box ${left} ${top} ${right} ${bottom}`;&amp;#10;el_tracker.sendCommand(draw_cmd);&amp;#10;el_tracker.sendMessage(`TRIALID ${trial_index}`);&amp;#10;status_msg = `TRIAL number ${trial_index}`;&amp;#10;el_tracker.sendCommand(`record_status_message '${status_msg}`);&amp;#10;while ((! dummy_mode)) {&amp;#10;    if (((! el_tracker.isConnected()) || el_tracker.breakPressed())) {&amp;#10;        terminate_task();&amp;#10;    }&amp;#10;    try {&amp;#10;        error = el_tracker.doDriftCorrect(Number.parseInt((scn_width / 2.0)), Number.parseInt((scn_height / 2.0)), 1, 1);&amp;#10;        if ((error !== pylink.ESC_KEY)) {&amp;#10;            break;&amp;#10;        }&amp;#10;    } catch(e) {&amp;#10;    }&amp;#10;}&amp;#10;el_tracker.setOfflineMode();&amp;#10;try {&amp;#10;    el_tracker.startRecording(1, 1, 1, 1);&amp;#10;} catch(error) {&amp;#10;    if ((error instanceof RuntimeError)) {&amp;#10;        console.log(&quot;ERROR:&quot;, error);&amp;#10;        abort_trial();&amp;#10;    } else {&amp;#10;        throw error;&amp;#10;    }&amp;#10;}&amp;#10;pylink.pumpDelay(100);&amp;#10;" valType="extendedCode" updates="constant" name="Begin JS Routine"/>
        <Param val="# This Begin Routine tab of the elStartRecord component  sets some variable&amp;#10;# values for the trial, draws some feedback graphics on the Host PC to represent&amp;#10;# stimulus positions, sends a trial start message to the EDF, performs &amp;#10;# drift check/drift correct, and starts eye tracker recording&amp;#10;&amp;#10;# get a reference to the currently active EyeLink connection&amp;#10;el_tracker = pylink.getEYELINK()&amp;#10;&amp;#10;# put the tracker in the offline mode first&amp;#10;el_tracker.setOfflineMode()&amp;#10;&amp;#10;# clear the host screen before we draw the backdrop&amp;#10;el_tracker.sendCommand('clear_screen 0')&amp;#10;&amp;#10;# width and height dimension of the videos&amp;#10;vid_w, vid_h = 800,600&amp;#10;&amp;#10;# path to the video&amp;#10;vidpath = os.path.join('videos/', video)&amp;#10;&amp;#10;# OPTIONAL: Drawing reference landmarks on the Host PC screen - Optional&amp;#10;# See COMMANDS.INI in the Host PC's exe folder for a list of commands&amp;#10;# region occupied by the movie&amp;#10;&amp;#10;mov_coords = (int(scn_width/2.0 - vid_w/2.0),&amp;#10;              int(scn_height/2.0 - vid_h/2.0),&amp;#10;              int(scn_width/2.0 + vid_w/2.0),&amp;#10;              int(scn_height/2.0 + vid_h/2.0))&amp;#10;              &amp;#10;# region occupied by the occluding wall&amp;#10;box_coords = (int(scn_width/2.0 - 80),&amp;#10;              int(scn_height/2.0 - 70),&amp;#10;              int(scn_width/2.0 + 80),&amp;#10;              int(scn_height/2.0 + 90))&amp;#10;&amp;#10;# mark the ball movement path&amp;#10;line_coords = (int(scn_width/2.0 - vid_w/2.0),&amp;#10;               int(scn_height/2.0 + 40),&amp;#10;               int(scn_width/2.0 + vid_w/2.0),&amp;#10;               int(scn_height/2.0 + 40))&amp;#10;&amp;#10;# send the drawing commands to the Host PC&amp;#10;el_tracker.sendCommand('draw_box %d %d %d %d 15' % mov_coords)&amp;#10;el_tracker.sendCommand('draw_box %d %d %d %d 15' % box_coords)&amp;#10;el_tracker.sendCommand('draw_line %d %d %d %d 15' % line_coords)&amp;#10;&amp;#10;# send a &quot;TRIALID&quot; message to mark the start of a trial, see Data&amp;#10;# Viewer User Manual, &quot;Protocol for EyeLink Data to Viewer Integration&quot;&amp;#10;el_tracker.sendMessage('TRIALID %d' % trial_index)&amp;#10;&amp;#10;# record_status_message : show some info on the Host PC&amp;#10;# here we show how many trial has been tested&amp;#10;status = trial_index, condition&amp;#10;status_msg = 'Trial number %i, Condition %s' % status&amp;#10;el_tracker.sendCommand(&quot;record_status_message '%s'&quot; % status_msg)&amp;#10;&amp;#10;# drift check&amp;#10;# we recommend drift-check at the beginning of each trial&amp;#10;# the doDriftCorrect() function requires target position in integers&amp;#10;# the last two arguments:&amp;#10;# draw_target (1-default, 0-draw the target then call doDriftCorrect)&amp;#10;# allow_setup (1-press ESCAPE to recalibrate, 0-not allowed)&amp;#10;dc_x = scn_width/2.0&amp;#10;dc_y = scn_height/2.0&amp;#10;&amp;#10;# Skip drift-check if running the script in Dummy Mode&amp;#10;while not dummy_mode:&amp;#10;    # terminate the task if no longer connected to the tracker or&amp;#10;    # user pressed Ctrl-C to terminate the task&amp;#10;    if (not el_tracker.isConnected()) or el_tracker.breakPressed():&amp;#10;        terminate_task(genv,edf_file,session_folder,session_identifier)&amp;#10;    # drift-check and re-do camera setup if ESCAPE is pressed&amp;#10;    try:&amp;#10;        error = el_tracker.doDriftCorrect(int(scn_width/2.0),&amp;#10;                                          int(scn_height/2.0), 1, 1)&amp;#10;        # break following a success drift-check&amp;#10;        if error is not pylink.ESC_KEY:&amp;#10;            break&amp;#10;    except:&amp;#10;        pass&amp;#10;&amp;#10;# put tracker in idle/offline mode before recording&amp;#10;el_tracker.setOfflineMode()&amp;#10;&amp;#10;# Start recording&amp;#10;# arguments: sample_to_file, events_to_file, sample_over_link,&amp;#10;# event_over_link (1-yes, 0-no)&amp;#10;try:&amp;#10;    el_tracker.startRecording(1, 1, 1, 1)&amp;#10;except RuntimeError as error:&amp;#10;    print(&quot;ERROR:&quot;, error)&amp;#10;    abort_trial()&amp;#10;&amp;#10;# Allocate some time for the tracker to cache some samples&amp;#10;pylink.pumpDelay(100)" valType="extendedCode" updates="constant" name="Begin Routine"/>
        <Param val="Py" valType="str" updates="None" name="Code Type"/>
        <Param val="" valType="extendedCode" updates="constant" name="Each Frame"/>
        <Param val="" valType="extendedCode" updates="constant" name="Each JS Frame"/>
        <Param val="" valType="extendedCode" updates="constant" name="End Experiment"/>
        <Param val="" valType="extendedCode" updates="constant" name="End JS Experiment"/>
        <Param val="" valType="extendedCode" updates="constant" name="End JS Routine"/>
        <Param val="" valType="extendedCode" updates="constant" name="End Routine"/>
        <Param val="False" valType="bool" updates="None" name="disabled"/>
        <Param val="elStartRecord" valType="code" updates="None" name="name"/>
      </CodeComponent>
    </Routine>
    <Routine name="eyelinkStopRecording">
      <RoutineSettingsComponent name="eyelinkStopRecording" plugin="None">
        <Param val="none" valType="str" updates="None" name="backgroundFit"/>
        <Param val="" valType="str" updates="None" name="backgroundImg"/>
        <Param val="$[0,0,0]" valType="color" updates="None" name="color"/>
        <Param val="rgb" valType="str" updates="None" name="colorSpace"/>
        <Param val="" valType="str" updates="constant" name="desc"/>
        <Param val="False" valType="bool" updates="None" name="disabled"/>
        <Param val="" valType="code" updates="None" name="durationEstim"/>
        <Param val="False" valType="code" updates="None" name="forceNonSlip"/>
        <Param val="eyelinkStopRecording" valType="code" updates="None" name="name"/>
        <Param val="True" valType="bool" updates="None" name="saveStartStop"/>
        <Param val="" valType="code" updates="constant" name="skipIf"/>
        <Param val="duration (s)" valType="str" updates="None" name="stopType"/>
        <Param val="" valType="code" updates="constant" name="stopVal"/>
        <Param val="False" valType="bool" updates="None" name="useWindowParams"/>
      </RoutineSettingsComponent>
      <CodeComponent name="elStopRecord" plugin="None">
        <Param val="" valType="extendedCode" updates="constant" name="Before Experiment"/>
        <Param val="" valType="extendedCode" updates="constant" name="Before JS Experiment"/>
        <Param val="" valType="extendedCode" updates="constant" name="Begin Experiment"/>
        <Param val="" valType="extendedCode" updates="constant" name="Begin JS Experiment"/>
        <Param val="" valType="extendedCode" updates="constant" name="Begin JS Routine"/>
        <Param val="" valType="extendedCode" updates="constant" name="Begin Routine"/>
        <Param val="Py" valType="str" updates="None" name="Code Type"/>
        <Param val="" valType="extendedCode" updates="constant" name="Each Frame"/>
        <Param val="" valType="extendedCode" updates="constant" name="Each JS Frame"/>
        <Param val="" valType="extendedCode" updates="constant" name="End Experiment"/>
        <Param val="" valType="extendedCode" updates="constant" name="End JS Experiment"/>
        <Param val="clear_screen(psychoJS.window);&amp;#10;el_tracker.sendMessage(&quot;blank_screen&quot;);&amp;#10;el_tracker.sendMessage(&quot;!V CLEAR 128 128 128&quot;);&amp;#10;offsetValue = Number.parseInt(Math.round(((core.getTime() - fixation.tStartRefresh) * 1000)));&amp;#10;el_tracker.sendMessage(`${offsetValue}`);&amp;#10;el_tracker.sendMessage(`${offsetValue}`);&amp;#10;el_tracker.sendMessage(`${offsetValue} !V DRAWLINE 255 255 255 ${((scn_width / 2) - 50)} ${(scn_height / 2)} ${((scn_width / 2) + 50)} ${(scn_height / 2)}`);&amp;#10;el_tracker.sendMessage(`${offsetValue} !V DRAWLINE 255 255 255 ${(scn_width / 2)} ${((scn_height / 2) - 50)} ${(scn_width / 2)} ${((scn_height / 2) + 50)}`);&amp;#10;offsetValue = Number.parseInt(Math.round(((core.getTime() - image.tStartRefresh) * 1000)));&amp;#10;el_tracker.sendMessage(`${offsetValue}`);&amp;#10;el_tracker.sendMessage(`${offsetValue}`);&amp;#10;el_tracker.sendMessage(`${offsetValue} !V IMGLOAD CENTER ../../${trialImage} ${(scn_width / 2)} ${(scn_height / 2)}`);&amp;#10;if ((! (resp.rt instanceof list))) {&amp;#10;    offsetValue = Number.parseInt(Math.round(((core.getTime() - (image.tStartRefresh + resp.rt)) * 1000)));&amp;#10;    el_tracker.sendMessage(`${offsetValue}`);&amp;#10;}&amp;#10;pylink.pumpDelay(100);&amp;#10;el_tracker.stopRecording();&amp;#10;el_tracker.sendMessage(`!V TRIAL_VAR condition ${condition}`);&amp;#10;el_tracker.sendMessage(`!V TRIAL_VAR identifier ${identifier}`);&amp;#10;el_tracker.sendMessage(`!V TRIAL_VAR image ${trialImage}`);&amp;#10;el_tracker.sendMessage(`!V TRIAL_VAR condition ${condition}`);&amp;#10;el_tracker.sendMessage(`!V TRIAL_VAR corrAns ${corrAns}`);&amp;#10;pylink.pumpDelay(1);&amp;#10;el_tracker.sendMessage(`!V TRIAL_VAR accuracy ${resp.corr}`);&amp;#10;el_tracker.sendMessage(`!V TRIAL_VAR keyPressed ${resp.keys}`);&amp;#10;console.log(resp.rt.toString());&amp;#10;if ((resp.rt instanceof list)) {&amp;#10;    el_tracker.sendMessage(&quot;!V TRIAL_VAR RT -1&quot;);&amp;#10;} else {&amp;#10;    el_tracker.sendMessage(`!V TRIAL_VAR RT ${Number.parseInt(Math.round((resp.rt * 1000)))}`);&amp;#10;}&amp;#10;el_tracker.sendMessage(`TRIAL_RESULT ${0}`);&amp;#10;trial_index = (trial_index + 1);&amp;#10;" valType="extendedCode" updates="constant" name="End JS Routine"/>
        <Param val="# This End Routine tab of the elStopRecord component clears the &amp;#10;# screen (and sends a message to mark the clearing), closes the trial's &amp;#10;# DRAW LIST file, stops recording, sends a trial end message, and increments &amp;#10;# a trial counter variable&amp;#10;&amp;#10;# clear the screen&amp;#10;clear_screen(win,genv)&amp;#10;&amp;#10;#send a message to mark the onset of the blank screen&amp;#10;el_tracker.sendMessage('blank_screen')&amp;#10;&amp;#10;# send a message to clear the Data Viewer screen as well&amp;#10;el_tracker.sendMessage('!V CLEAR 128 128 128')&amp;#10;&amp;#10;# close the VCL file that contain the VFRAME messages&amp;#10;dlf_file.close()&amp;#10;&amp;#10;# stop recording; add 100 msec to catch final events before stopping&amp;#10;pylink.pumpDelay(100)&amp;#10;el_tracker.stopRecording()&amp;#10;    &amp;#10;# send a 'TRIAL_RESULT' message to mark the end of trial, see Data&amp;#10;# Viewer User Manual, &quot;Protocol for EyeLink Data to Viewer Integration&quot;&amp;#10;el_tracker.sendMessage('TRIAL_RESULT %d' % 0)&amp;#10;&amp;#10;# update the trial counter for the next trial&amp;#10;trial_index = trial_index + 1" valType="extendedCode" updates="constant" name="End Routine"/>
        <Param val="False" valType="bool" updates="None" name="disabled"/>
        <Param val="elStopRecord" valType="code" updates="None" name="name"/>
      </CodeComponent>
    </Routine>
  </Routines>
  <Flow>
    <Routine name="eyelinkSetup"/>
    <Routine name="instruct"/>
    <LoopInitiator loopType="TrialHandler" name="trials">
      <Param name="Selected rows" updates="None" val="" valType="str"/>
      <Param name="conditions" updates="None" val="[OrderedDict([('identifier', 1), ('condition', 'Disappear'), ('video', 'disappear.avi')]), OrderedDict([('identifier', 2), ('condition', 'Expected'), ('video', 'expected.avi')]), OrderedDict([('identifier', 3), ('condition', 'Violation'), ('video', 'violation.avi')])]" valType="str"/>
      <Param name="conditionsFile" updates="None" val="trialData.csv" valType="file"/>
      <Param name="endPoints" updates="None" val="[0, 1]" valType="num"/>
      <Param name="isTrials" updates="None" val="True" valType="bool"/>
      <Param name="loopType" updates="None" val="random" valType="str"/>
      <Param name="nReps" updates="None" val="1" valType="num"/>
      <Param name="name" updates="None" val="trials" valType="code"/>
      <Param name="random seed" updates="None" val="" valType="code"/>
    </LoopInitiator>
    <Routine name="eyelinkStartRecording"/>
    <Routine name="trial"/>
    <Routine name="eyelinkStopRecording"/>
    <LoopTerminator name="trials"/>
    <Routine name="thanks"/>
  </Flow>
</PsychoPy2experiment>
