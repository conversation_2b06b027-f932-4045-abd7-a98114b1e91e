#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
简化的EyeLink校准和验证测试程序
专注于验证功能：显示随机光点和实时眼动目标
"""

import pylink
import random
import time
from psychopy import visual, core, event, monitors

class SimpleCalibrationTest:
    """简化的校准测试类"""
    
    def __init__(self, dummy_mode=True):
        """
        初始化
        
        Args:
            dummy_mode: 是否使用虚拟模式
        """
        self.dummy_mode = dummy_mode
        self.el_tracker = None
        self.win = None
        
        # 验证参数
        self.dot_positions = []
        self.current_dot_index = 0
        self.dot_switch_interval = 3.0  # 光点切换间隔（秒）
        self.last_switch_time = 0
        
        # 显示参数
        self.dot_size = 20
        self.gaze_size = 15
        self.dot_color = 'white'
        self.gaze_color = 'red'
        self.background_color = 'gray'
    
    def connect_eyelink(self):
        """连接EyeLink眼动仪"""
        try:
            if self.dummy_mode:
                print("使用虚拟模式连接EyeLink...")
                self.el_tracker = pylink.EyeLink(None)
            else:
                print("连接EyeLink主机: 100.1.1.1")
                self.el_tracker = pylink.EyeLink("100.1.1.1")
            
            print("✓ EyeLink连接成功")
            return True
            
        except RuntimeError as error:
            print(f"✗ EyeLink连接失败: {error}")
            return False
    
    def setup_display(self):
        """设置显示窗口"""
        try:
            # 创建窗口（非全屏，便于测试）
            self.win = visual.Window(
                size=(800, 600),
                fullscr=False,
                units='pix',
                color=self.background_color
            )
            
            scn_width, scn_height = self.win.size
            print(f"屏幕分辨率: {scn_width} x {scn_height}")
            
            # 发送屏幕坐标给EyeLink
            if self.el_tracker:
                el_coords = f"screen_pixel_coords = 0 0 {scn_width - 1} {scn_height - 1}"
                self.el_tracker.sendCommand(el_coords)
            
            print("✓ 显示窗口设置成功")
            return True
            
        except Exception as e:
            print(f"✗ 显示窗口设置失败: {e}")
            return False
    
    def generate_dot_positions(self, num_positions=9):
        """生成随机光点位置"""
        scn_width, scn_height = self.win.size
        margin = 50
        
        self.dot_positions = []
        for _ in range(num_positions):
            x = random.randint(-scn_width//2 + margin, scn_width//2 - margin)
            y = random.randint(-scn_height//2 + margin, scn_height//2 - margin)
            self.dot_positions.append((x, y))
        
        print(f"生成了 {len(self.dot_positions)} 个随机光点位置")
    
    def get_current_gaze_position(self):
        """获取当前眼动位置（模拟）"""
        if self.dummy_mode:
            # 模拟眼动数据：在当前光点附近随机偏移
            if self.dot_positions:
                dot_pos = self.dot_positions[self.current_dot_index]
                # 添加一些随机噪声
                x = dot_pos[0] + random.randint(-30, 30)
                y = dot_pos[1] + random.randint(-30, 30)
                return (x, y)
        else:
            # 真实眼动数据获取
            try:
                dt = self.el_tracker.getNewestSample()
                if dt is not None:
                    if dt.isLeftSample():
                        gaze_pos = dt.getLeftEye().getGaze()
                    elif dt.isRightSample():
                        gaze_pos = dt.getRightEye().getGaze()
                    else:
                        return None
                    
                    # 转换坐标系
                    scn_width, scn_height = self.win.size
                    x = gaze_pos[0] - scn_width // 2
                    y = scn_height // 2 - gaze_pos[1]
                    return (x, y)
            except:
                pass
        
        return None
    
    def run_validation(self, duration=30):
        """运行验证程序"""
        try:
            print(f"\n开始验证程序，持续 {duration} 秒...")
            print("- 白色光点每3秒切换位置")
            print("- 红色圆圈显示当前的注视位置")
            print("- 按 Esc 键退出")
            
            # 生成光点位置
            self.generate_dot_positions()
            
            # 开始记录眼动数据
            if self.el_tracker and not self.dummy_mode:
                self.el_tracker.startRecording(1, 1, 1, 1)
                pylink.pumpDelay(100)
            
            # 创建显示对象
            dot_stim = visual.Circle(
                self.win,
                radius=self.dot_size,
                fillColor=self.dot_color,
                lineColor=self.dot_color
            )
            
            gaze_stim = visual.Circle(
                self.win,
                radius=self.gaze_size,
                fillColor=None,
                lineColor=self.gaze_color,
                lineWidth=3
            )
            
            # 添加文本提示
            info_text = visual.TextStim(
                self.win,
                text="",
                font='Arial',
                height=20,
                color='white',
                pos=(0, -250)
            )
            
            # 验证循环
            start_time = time.time()
            self.last_switch_time = start_time
            self.current_dot_index = 0
            
            while time.time() - start_time < duration:
                current_time = time.time()
                
                # 检查是否需要切换光点位置
                if current_time - self.last_switch_time >= self.dot_switch_interval:
                    self.current_dot_index = (self.current_dot_index + 1) % len(self.dot_positions)
                    self.last_switch_time = current_time
                    print(f"切换到位置 {self.current_dot_index + 1}: {self.dot_positions[self.current_dot_index]}")
                
                # 设置当前光点位置
                dot_pos = self.dot_positions[self.current_dot_index]
                dot_stim.pos = dot_pos
                
                # 获取当前眼动位置
                gaze_pos = self.get_current_gaze_position()
                
                # 更新信息文本
                remaining_time = int(duration - (current_time - start_time))
                info_text.text = f"剩余时间: {remaining_time}秒 | 当前光点: {self.current_dot_index + 1}/{len(self.dot_positions)}"
                
                # 清屏并绘制
                self.win.color = self.background_color
                
                # 绘制光点
                dot_stim.draw()
                
                # 绘制眼动位置（如果有效）
                if gaze_pos:
                    gaze_stim.pos = gaze_pos
                    gaze_stim.draw()
                
                # 绘制信息文本
                info_text.draw()
                
                # 显示帧
                self.win.flip()
                
                # 检查退出键
                keys = event.getKeys()
                if 'escape' in keys:
                    print("用户按下Esc键，退出验证")
                    break
                
                # 控制帧率
                core.wait(0.016)  # 约60fps
            
            # 停止记录
            if self.el_tracker and not self.dummy_mode:
                self.el_tracker.stopRecording()
            
            print("✓ 验证程序完成")
            return True
            
        except Exception as e:
            print(f"✗ 验证程序出错: {e}")
            return False
    
    def cleanup(self):
        """清理资源"""
        try:
            if self.el_tracker:
                self.el_tracker.close()
            
            if self.win:
                self.win.close()
            
            print("✓ 资源清理完成")
            
        except Exception as e:
            print(f"清理资源时出错: {e}")

def main():
    """主函数"""
    print("="*50)
    print("简化EyeLink校准验证测试程序")
    print("="*50)
    
    # 获取用户输入
    use_dummy = input("是否使用虚拟模式? (y/n, 默认y): ").strip().lower()
    use_dummy = use_dummy != 'n'  # 默认为True
    
    duration_input = input("验证持续时间(秒, 默认30): ").strip()
    try:
        duration = int(duration_input) if duration_input else 30
    except ValueError:
        duration = 30
    
    # 创建测试对象
    tester = SimpleCalibrationTest(use_dummy)
    
    try:
        # 1. 连接EyeLink
        if not tester.connect_eyelink():
            return
        
        # 2. 设置显示
        if not tester.setup_display():
            return
        
        # 3. 运行验证
        print("\n按任意键开始验证...")
        input()
        
        tester.run_validation(duration)
        
        print("\n程序执行完成！")
        
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"\n程序出现错误: {e}")
    finally:
        # 清理资源
        tester.cleanup()

if __name__ == "__main__":
    main()
